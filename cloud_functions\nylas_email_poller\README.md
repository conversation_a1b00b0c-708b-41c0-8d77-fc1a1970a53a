# Nylas Email Poller Cloud Function

**Production-ready thread-based email processing for DRCR**

## Overview

This Cloud Function provides reliable email conversation threading using the Nylas Threads API. It runs every 5 minutes via Cloud Scheduler to process email chains as proper conversations.

## Architecture

```
Cloud Scheduler (5 min) → Cloud Function → Nylas Threads API → Firestore
                                              ↓
                                    Thread-based Processing
                                              ↓
                              EMAIL_THREADS + EMAILS + REPLIES
                                              ↓
                          Cloud Tasks Queue → Reply Processing
                                              ↓
                                    Nylas Send API → Gmail
```

## Features

- ✅ **Thread-based email chains** using Nylas Threads API
- ✅ **Email reply system** with Cloud Tasks async processing
- ✅ **Triple Firestore schema** (EMAIL_THREADS + EMAILS + REPLIES)
- ✅ **Race-safe deduplication** with create() operations
- ✅ **Entity-based routing** via plus-addressing
- ✅ **Template-based replies** for DRCR workflows
- ✅ **Rate limiting** (1 msg/sec) for Gmail compliance
- ✅ **Optimized attachment metadata** storage
- ✅ **Performance metrics** and monitoring
- ✅ **Batch entity validation** for performance
- ✅ **Retry logic** with exponential backoff

## Deployment

### Prerequisites
- GCP authentication: `gcloud auth login`
- Project set: `gcloud config set project drcr-d660a`

### Deploy Function
```powershell
.\deploy.ps1
```

**Note**: Cloud Scheduler is already configured and running every 10 minutes.

## Testing

### Threading Test
```powershell
.\test_threading.ps1
```

### Manual Test
```bash
curl -X POST https://europe-west2-drcr-d660a.cloudfunctions.net/nylas_email_poller \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

## Monitoring

### Function Logs
```bash
gcloud functions logs read nylas_email_poller --region europe-west2 --limit 50
```

### Scheduler Logs
```bash
gcloud logging read "resource.type=cloud_scheduler_job" --limit 20
```

## Configuration

### Environment Variables
- `GCP_PROJECT_ID`: GCP project ID
- `NYLAS_API_KEY`: Nylas API key  
- `NYLAS_GRANT_ID`: Nylas grant ID
- `POLLING_BATCH_SIZE`: Messages per poll (default: 50)

### Polling Frequency
Default: Every 5 minutes (8,640 calls/month)

To change frequency, update the Cloud Scheduler job:
```bash
gcloud scheduler jobs update http nylas-email-polling --schedule="*/2 * * * *"
```

## Performance

### Expected Metrics
- **Latency**: <2 seconds per poll (optimized)
- **Throughput**: 50 threads per poll
- **Success Rate**: >99%
- **Cost**: ~$5-10/month

### Scaling
- **Light load**: Every 5 minutes
- **Heavy load**: Every 2 minutes  
- **Burst capacity**: 100+ threads per poll

## Troubleshooting

### Common Issues
1. **No threads processed**: Check entity IDs in thread participants
2. **API timeouts**: Increase POLLING_TIMEOUT_SECONDS
3. **Rate limits**: Reduce polling frequency
4. **Race conditions**: Fixed with create() operations
5. **Large documents**: Attachment metadata trimmed automatically

### Debug Commands
```bash
# Check function health
curl https://europe-west2-drcr-d660a.cloudfunctions.net/nylas_email_poller

# Check recent threads in Firestore  
# Navigate to ENTITIES/{entity_id}/EMAIL_THREADS collection

# Check scheduler job status  
gcloud scheduler jobs describe nylas-email-polling --location europe-west2
```

## File Structure

```
nylas_email_poller/
├── main.py                    # Cloud Function entry point
├── requirements.txt           # Python dependencies
├── deploy.ps1                # Deployment script
├── .env.example              # Environment template
├── services/
│   ├── nylas_service.py      # Nylas API client
│   └── email_processor.py    # Email processing logic
└── utils/
    └── error_handler.py      # Error handling
```

## Data Structure

### EMAIL_THREADS Collection
```javascript
ENTITIES/{entity_id}/EMAIL_THREADS/{thread_id}
{
  thread_id: "thread_123",
  entity_id: "entity_456", 
  subject: "Re: Invoice Processing",
  participant_emails: ["<EMAIL>", "<EMAIL>"],
  message_count: 3,
  has_attachments: true,
  first_message_at: "2025-07-20T10:30:00Z",
  latest_message_at: "2025-07-20T14:45:00Z",
  thread_status: "active"
}
```

### EMAILS Collection  
```javascript
ENTITIES/{entity_id}/EMAILS/{email_id}
{
  email_id: "email_789",
  thread_id: "thread_123",
  subject: "Re: Invoice Processing", 
  from_address: ["<EMAIL>"],
  attachments: [
    {id: "att_1", filename: "invoice.pdf", content_type: "application/pdf", size: 45678}
  ],
  received_at: "2025-07-20T14:45:00Z"
}
```

### REPLIES Collection (New)
```javascript
ENTITIES/{entity_id}/EMAIL_THREADS/{thread_id}/REPLIES/{reply_id}
{
  reply_id: "reply_456",
  thread_id: "thread_123",
  entity_id: "entity_456",
  to_email: "<EMAIL>",
  subject: "Re: Invoice Processing - Received",
  body: "Thank you for sending your invoice...",
  reply_to_message_id: "email_789",
  external_message_id: "nylas_msg_123",
  status: "sent",
  template_used: "acknowledgment.document_received",
  created_at: "2025-07-20T15:00:00Z",
  sent_at: "2025-07-20T15:00:15Z",
  created_by: "user_123"
}
```

## Reply System

### Available Templates
- **Acknowledgment**: Document received, multiple documents
- **Status Updates**: Processing complete, exported to Xero  
- **Error Handling**: Attachment issues, missing files
- **Information Requests**: Need more details

### Usage Example
```python
from services.reply_processor import ReplyProcessor

processor = ReplyProcessor()

# Send templated reply
reply_data = {
    "entity_id": "entity_456",
    "thread_id": "thread_123", 
    "to_email": "<EMAIL>",
    "reply_to_message_id": "email_789",
    "template": "acknowledgment.document_received",
    "variables": {"original_subject": "Invoice Processing"}
}

result = processor.process_reply_sync(reply_data)
```

## Next Steps

1. ✅ **Threading implementation** complete and deployed
2. ✅ **Reply system infrastructure** implemented
3. ✅ **Cloud Tasks queue** configured for rate limiting
4. 🔄 **REST API integration** for frontend
5. 📋 **Auto-reply triggers** based on document processing

---

**Status**: ✅ LIVE IN PRODUCTION with threading & reply system ready
**Replaces**: Message-based polling (now supports proper email chains + replies)  
**Architecture**: Thread-first polling + async reply processing with Cloud Tasks
**Recent Updates**: Email reply system, Cloud Tasks integration, template management, Firestore REPLIES schema