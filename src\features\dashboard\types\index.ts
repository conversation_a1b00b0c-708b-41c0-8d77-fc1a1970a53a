import type { ClientSummary, EntitySummary } from '@/lib/api';

export interface FirmDashboardProps {
  firmId: string;
  firmName?: string;
  isAdmin?: boolean;
}

export interface DashboardFilters {
  clientFilter: string;
  statusFilter: string;
  statusFilters: string[]; // New multi-select filters
  page: number;
  pageSize: number;
}

export interface StatusInfo {
  badgeClass: string;
  icon: React.ReactNode;
  text: string;
  tooltip?: string | null;
}

// Operation state type from XeroOperationStatus hook
export interface XeroOperationState {
  status: 'idle' | 'loading' | 'success' | 'error' | 'timeout';
  elapsedSeconds: number;
  operation: 'connect' | 'disconnect' | 'reconnect' | 'sync';
  startOperation: (operationType: 'connect' | 'disconnect' | 'reconnect' | 'sync') => void;
  completeOperation: (success: boolean) => void;
  resetOperation: () => void;
  retryOperation: () => { startOperation: any };
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  isTimeout: boolean;
}

export interface PaginationData {
  current_page?: number;
  currentPage?: number;
  total_pages?: number;
  totalPages?: number;
  total_items?: number;
  totalItems?: number;
  page_size?: number;
  pageSize?: number;
}

export interface DashboardState {
  clientSummaries: ClientSummary[];
  pagination: PaginationData | null;
  isLoading: boolean;
  error: string | null;
  expandedClients: Record<string, boolean>;

  // Modal states
  connectModalOpen: boolean;
  settingsModalOpen: boolean;
  clientSettingsModalOpen: boolean;
  selectedClientId: string | null;
  selectedEntityId: string | null;

  // Operation states
  disconnectState: XeroOperationState;
  connectState: XeroOperationState;
  operatingEntityId: string | null;
}

export interface DashboardActions {
  handleClientClick: (clientId: string) => void;
  handleEntityClick: (clientId: string, entityId: string) => void;
  handleAddNewClient: () => void;
  handleClientSettings: (clientId: string) => void;
  handleDisconnectEntity: (clientId: string, entityId: string) => Promise<void>;
  handleFixConnection: (clientId: string, entityId: string) => Promise<void>;
  handleConnectNewEntity: (clientId: string) => void;
  handleEntitySettings: (clientId: string, entityId: string) => void;
  handleInitiateConnection: (clientId: string, provider: 'xero' | 'qbo') => Promise<void>;
  toggleClientExpansion: (clientId: string) => void;
}

export type { ClientSummary, EntitySummary };