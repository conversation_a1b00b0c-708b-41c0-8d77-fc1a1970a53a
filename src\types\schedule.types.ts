// Schedule Status Enums - Updated for consistency with backend
export enum ScheduleStatus {
  // Initial states - require configuration/review
  PENDING_CONFIGURATION = 'pending_configuration', // LLM-detected, needs account setup
  PENDING_CONFIRMATION = 'pending_confirmation',   // Reviewed, ready for confirmation
  
  // Active states
  CONFIRMED = 'confirmed',                         // Confirmed, ready for posting
  POSTED = 'posted',                              // Successfully posted to accounting system
  PARTIALLY_POSTED = 'partially_posted',          // Some entries posted, some pending
  
  // Terminal states
  SKIPPED = 'skipped',                            // User chose to skip
  CANCELLED = 'cancelled',                        // Cancelled by user or system
  ERROR = 'error',                                // Error occurred during processing
  
  // Legacy/compatibility statuses
  PROPOSED = 'proposed',                          // Legacy - maps to PENDING_REVIEW
  REQUIRES_ACTION = 'requires_action',            // Legacy - maps to PENDING_CONFIGURATION
  VALIDATION_FAILED = 'validation_failed',       // Legacy - maps to ERROR
  FULLY_POSTED = 'fully_posted',                 // Legacy - maps to POSTED
  EXCLUDED = 'excluded',                          // Line item excluded from amortization
  ERROR_POSTING = 'error_posting'                 // Legacy - maps to ERROR
}

export enum EntryStatus {
  PROPOSED = 'proposed',                          // Entry is proposed/planned
  CONFIRMED = 'confirmed',                        // Entry is confirmed for posting
  POSTED = 'posted',                             // Entry successfully posted
  POSTING = 'posting',                           // Entry is currently being posted
  POSTING_ERROR = 'posting_error',               // Error occurred during posting
  MATCHED_MANUAL = 'matched_manual',             // Manually matched to existing journal
  SKIPPED = 'skipped'                            // Entry was skipped
}

export enum InvoiceStatus {
  ACTION_NEEDED = 'action_needed',               // Requires user action
  PROPOSED = 'proposed',                         // Initial state
  CONFIRMED = 'confirmed',                       // All schedules confirmed
  VALIDATION_ERROR = 'validation_error',         // Validation issues
  FULLY_POSTED = 'fully_posted',               // All schedules posted
  PARTIALLY_POSTED = 'partially_posted',        // Some schedules posted
  SKIPPED = 'skipped',                          // All schedules skipped
  PROCESSING = 'processing'                      // Currently being processed
}

export enum SupplierStatus {
  ACTION_NEEDED = 'action_needed',               // Has invoices requiring action
  PROPOSED = 'proposed',                         // Initial state
  CONFIRMED = 'confirmed',                       // All invoices confirmed
  VALIDATION_ERROR = 'validation_error',         // Has validation errors
  OK = 'ok'                                     // All invoices processed successfully
}

// Schedule Types
export interface MonthlyEntry {
  amount: number;
  month_date: string; // ISO date string (snake_case per backend)
  status: 'pending_configuration' | 'proposed' | 'posted' | 'skipped' | 'error';
  last_action_by_user_id?: string | null;
  last_action_timestamp?: string | null;
  match_confidence?: number | null;
  posted_journal_id?: string | null;
  posted_journal_line_id?: string | null;
  posting_error?: string | null;
}

export interface MonthlyScheduleEntry {
  period: number;
  date: string;
  amount: number;
  originalAmount: number | null; // null = never previewed, number = baseline amount
  status: 'proposed' | 'confirmed' | 'posted';
  runningBalance: number;
}

// Helper function to check if entry was manually edited
export const isManuallyEdited = (entry: MonthlyScheduleEntry): boolean => 
  entry.originalAmount !== null && entry.amount !== entry.originalAmount;

export interface AmortizableLineItem {
  lineItemId: string;
  description: string;
  lineAmount: number;
  scheduleId: string;
  prepaymentAccountCode: string;
  expenseAccountCode: string | null;
  /** Amortization calculation method returned by backend (e.g. "equal_monthly" | "day_based") */
  calculationMethod?: 'day_based' | 'equal_monthly';
  overallStatus: ScheduleStatus;
  monthlyBreakdown: Record<string, MonthlyEntry>;
}

export interface InvoiceData {
  invoiceId: string;
  reference: string;
  invoiceDate: string;
  totalAmount: number;
  currencyCode: string;
  hasAttachment: boolean;
  attachmentId?: string;
  ocrWarningMessage?: string | null;
  overallStatus: InvoiceStatus;
  isPartialApplication: boolean;
  amortizableLineItems: AmortizableLineItem[];
}

export interface SupplierData {
  supplierId: string;
  supplierName: string;
  overallStatus: SupplierStatus;
  invoices: InvoiceData[];
}

export interface ScheduleData {
  schedule_id: string;
  transaction_id: string;
  line_item_id?: string;
  status: 'pending_configuration' | 'proposed' | 'confirmed' | 'posted' | 'cancelled' | 'skipped' | 'error';
  
  // Amount & dates
  amount: number;
  start_date: string;
  end_date: string;
  number_of_periods: number;
  
  // Account codes (snake_case per BACKEND_UPDATE_PROMPT.md)
  account_code?: string;           // Asset/prepayment account
  expense_account_code?: string;   // Expense account
  
  // Description
  description?: string;
  
  // Custom narration for journal entries
  custom_narration?: string;
  
  // New fields from backend update
  calculation_method?: 'day_based' | 'equal_monthly';
  detection_method?: 'llm_only' | 'gl_coding';
  
  // Monthly breakdown
  monthly_entries?: MonthlyEntry[];
  
  // Timestamps
  created_at?: string;
  updated_at?: string;
}

// Filter and Pagination Types
export interface PrepaymentsFilters {
  client_id: string;
  entity_id?: string;
  page?: number;
  limit?: number;
  supplier_filter?: string;
  show_only_pending?: boolean;
  status_filter?: string;
  status_filters?: string[];
}

export interface PaginationData {
  currentPage: number;
  pageSize?: number;
  totalItems?: number;
  totalPages?: number;
  itemsPerPage?: number;
}

export interface PrepaymentsResponse {
  suppliers: SupplierData[];
  pagination: PaginationData;
}

// Status Mapping Utilities
export const mapBackendScheduleStatus = (status: string): ScheduleStatus => {
  switch (status) {
    case 'pending_configuration':
      return ScheduleStatus.PENDING_CONFIGURATION;
    case 'proposed':
      return ScheduleStatus.PROPOSED;
    case 'pending_confirmation':
      return ScheduleStatus.PENDING_CONFIRMATION;
    case 'confirmed':
      return ScheduleStatus.CONFIRMED;
    case 'posted':
    case 'fully_posted': // Legacy mapping
      return ScheduleStatus.POSTED;
    case 'partially_posted':
      return ScheduleStatus.PARTIALLY_POSTED;
    case 'skipped':
      return ScheduleStatus.SKIPPED;
    case 'cancelled':
      return ScheduleStatus.CANCELLED;
    case 'error':
    case 'validation_failed': // Legacy mapping
    case 'error_posting': // Legacy mapping
      return ScheduleStatus.ERROR;
    case 'excluded':
      return ScheduleStatus.EXCLUDED;
    case 'requires_action': // Legacy mapping
      return ScheduleStatus.PENDING_CONFIGURATION;
    default:
      return ScheduleStatus.PROPOSED; // Default fallback
  }
};

export const mapBackendEntryStatus = (status: string): EntryStatus => {
  switch (status) {
    case 'posted':
      return EntryStatus.POSTED;
    case 'confirmed':
      return EntryStatus.CONFIRMED;
    case 'posting_error':
      return EntryStatus.POSTING_ERROR;
    case 'matched_manual':
      return EntryStatus.MATCHED_MANUAL;
    case 'skipped':
      return EntryStatus.SKIPPED;
    case 'posting':
      return EntryStatus.POSTING;
    default:
      return EntryStatus.PROPOSED;
  }
};

// Status Classification Helpers
export const isActionNeededStatus = (status: ScheduleStatus): boolean => {
  return [
    ScheduleStatus.PENDING_CONFIGURATION,
    ScheduleStatus.PROPOSED,
    ScheduleStatus.REQUIRES_ACTION // Legacy
  ].includes(status);
};

export const isEditableStatus = (status: ScheduleStatus): boolean => {
  return [
    ScheduleStatus.PENDING_CONFIGURATION,
    ScheduleStatus.PROPOSED
  ].includes(status);
};

export const isConfirmableStatus = (status: ScheduleStatus): boolean => {
  return [
    ScheduleStatus.PENDING_CONFIGURATION,
    ScheduleStatus.PROPOSED
  ].includes(status);
};

export const isSkippableStatus = (status: ScheduleStatus): boolean => {
  return [
    ScheduleStatus.PENDING_CONFIGURATION,
    ScheduleStatus.PROPOSED,
    ScheduleStatus.CONFIRMED
  ].includes(status);
};

export const isTerminalStatus = (status: ScheduleStatus): boolean => {
  return [
    ScheduleStatus.POSTED,
    ScheduleStatus.SKIPPED,
    ScheduleStatus.CANCELLED,
    ScheduleStatus.ERROR,
    ScheduleStatus.EXCLUDED
  ].includes(status);
};

export const isActionableInvoiceStatus = (status: InvoiceStatus): boolean => {
  return [
    InvoiceStatus.ACTION_NEEDED,
    InvoiceStatus.PROPOSED,
    InvoiceStatus.PARTIALLY_POSTED
  ].includes(status);
};

// Aggregate status calculation for multiple schedules
export const calculateAggregateScheduleStatus = (statuses: ScheduleStatus[]): ScheduleStatus => {
  if (statuses.length === 0) {
    return ScheduleStatus.PENDING_CONFIGURATION;
  }

  // Priority order: Error states > Action needed > Proposed > Confirmed > Skipped > Posted

  // Check for error states first
  if (statuses.some(s => [
    ScheduleStatus.ERROR,
    ScheduleStatus.VALIDATION_FAILED,
    ScheduleStatus.ERROR_POSTING
  ].includes(s))) {
    return ScheduleStatus.ERROR;
  }

  // Check for action needed states
  if (statuses.some(s => [
    ScheduleStatus.PENDING_CONFIGURATION,
    ScheduleStatus.REQUIRES_ACTION
  ].includes(s))) {
    return ScheduleStatus.PENDING_CONFIGURATION;
  }

  if (statuses.some(s => s === ScheduleStatus.PROPOSED)) {
    return ScheduleStatus.PROPOSED;
  }

  // Check for confirmed state
  if (statuses.some(s => s === ScheduleStatus.CONFIRMED)) {
    return ScheduleStatus.CONFIRMED;
  }

  // Check for partial posting
  if (statuses.some(s => s === ScheduleStatus.PARTIALLY_POSTED)) {
    return ScheduleStatus.PARTIALLY_POSTED;
  }

  // If all are skipped
  if (statuses.every(s => s === ScheduleStatus.SKIPPED)) {
    return ScheduleStatus.SKIPPED;
  }

  // If all are posted
  if (statuses.every(s => s === ScheduleStatus.POSTED || s === ScheduleStatus.FULLY_POSTED)) {
    return ScheduleStatus.POSTED;
  }

  // Default fallback
  return ScheduleStatus.PROPOSED;
};

// Status Badge Configuration
export interface StatusBadgeConfig {
  variant: 'default' | 'secondary' | 'destructive' | 'outline';
  className: string;
  label: string;
}

export const getScheduleStatusConfig = (status: ScheduleStatus): StatusBadgeConfig => {
  switch (status) {
    case ScheduleStatus.PENDING_CONFIGURATION:
      return {
        variant: 'outline',
        className: 'text-orange-600 border-orange-300 bg-orange-50',
        label: 'Pending Configuration'
      };
    case ScheduleStatus.PROPOSED:
      return {
        variant: 'outline',
        className: 'text-yellow-600 border-yellow-300 bg-yellow-50',
        label: 'Proposed'
      };
    case ScheduleStatus.PENDING_CONFIRMATION:
      return {
        variant: 'outline',
        className: 'text-blue-600 border-blue-300 bg-blue-50',
        label: 'Pending Confirmation'
      };
    case ScheduleStatus.CONFIRMED:
      return {
        variant: 'default',
        className: 'bg-blue-100 text-blue-700',
        label: 'Confirmed'
      };
    case ScheduleStatus.POSTED:
    case ScheduleStatus.FULLY_POSTED:
      return {
        variant: 'default',
        className: 'bg-green-600 text-white',
        label: 'Posted'
      };
    case ScheduleStatus.PARTIALLY_POSTED:
      return {
        variant: 'default',
        className: 'bg-green-100 text-green-700',
        label: 'Partially Posted'
      };
    case ScheduleStatus.SKIPPED:
      return {
        variant: 'secondary',
        className: 'text-gray-600',
        label: 'Skipped'
      };
    case ScheduleStatus.CANCELLED:
      return {
        variant: 'secondary',
        className: 'text-gray-600',
        label: 'Cancelled'
      };
    case ScheduleStatus.ERROR:
    case ScheduleStatus.VALIDATION_FAILED:
    case ScheduleStatus.ERROR_POSTING:
      return {
        variant: 'destructive',
        className: '',
        label: 'Error'
      };
    case ScheduleStatus.EXCLUDED:
      return {
        variant: 'secondary',
        className: 'text-gray-500',
        label: 'Excluded'
      };
    case ScheduleStatus.REQUIRES_ACTION:
      return {
        variant: 'outline',
        className: 'text-orange-600 border-orange-300 bg-orange-50',
        label: 'Requires Action'
      };
    default:
      return {
        variant: 'outline',
        className: '',
        label: status
      };
  }
}; 