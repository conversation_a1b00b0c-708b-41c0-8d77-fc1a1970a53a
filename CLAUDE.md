# CLAUDE.md - DRCR Frontend Essential Context

## Project Overview
React-based financial data management platform for automated prepayment processing, Xero integration, and amortization schedule generation.

**Tech Stack:** React 18.3.1, TypeScript, Vite, Tailwind CSS, shadcn/ui, Firebase Auth, Zustand, Axios
**Architecture:** Service layer pattern with 9 domain services, component-based UI, centralized state management

## Critical API Information

### Authentication
- **Base URL:** `http://localhost:8081` (dev) / `https://drcr-d660a.web.app` (prod)
- **Auth:** Bearer token via Firebase ID token in Authorization header
- **Content-Type:** `application/json`

### Most Used API Endpoints
```typescript
// Prepayments (Primary Feature)
GET /transactions/dashboard - Get prepayments dashboard data
GET /transactions/ - Get all transactions (with AI recommendations)
POST /schedules/calculate-preview - Calculate amortization preview
PUT /schedules/{id} - Update schedule
POST /schedules/{id}/confirm - Confirm schedule
POST /schedules/{id}/entries/bulk-post - Post to Xero

// Client/Entity Management
GET /clients/summary - Get clients with pagination
GET /entities/?client_id={id} - Get entities for client
GET /entities/{id}/accounts - Get chart of accounts

// Xero Integration
GET /xero/connect/initiate/{clientId} - Start Xero OAuth
POST /xero/clients/{id}/xero/connect-organization - Connect Xero org
```

### Schedule Status System
**Flow:** `pending_configuration → pending_confirmation → confirmed → posted`
**Actionable:** `pending_configuration`, `pending_confirmation`, `proposed`

### AI Recommendation System
**Recommendations:** `create_prepayment`, `pending_analysis`, `no_prepayment`, `analysis_failed`, `skipped`
**Confidence:** AI confidence scores (0-1) with color coding: High (0.8+), Medium (0.5-0.8), Low (0-0.5)
**Skip Reasons:** `below_scanning_threshold`, `existing_release_journal_detected`, `all_line_items_excluded`

## Key Development Patterns

### Service Layer Usage
```typescript
// All API calls go through services
import { PrepaymentsService } from '@/services/prepayments.service';
import { ClientsService } from '@/services/clients.service';

// Standard error handling
try {
  const result = await PrepaymentsService.getPrepaymentsData(filters);
} catch (error) {
  toast.error(error.message);
}
```

### Component Patterns
```typescript
// Use shadcn/ui components
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

// State management with Zustand
import { useAuthStore } from '@/store/auth.store';

// Form handling
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
```

### Data Fetching
```typescript
// Standard loading pattern
const [loading, setLoading] = useState(false);
const [data, setData] = useState(null);

const fetchData = async () => {
  setLoading(true);
  try {
    const result = await ServiceName.methodName(params);
    setData(result);
  } catch (error) {
    // Handle error
  } finally {
    setLoading(false);
  }
};
```

## AI Code Templates (Copy-Paste Ready)

### New API Service Method
```typescript
async methodName(params: RequestType): Promise<ResponseType> {
  try {
    const response = await this.api.post('/endpoint', params);
    return response.data;
  } catch (error) {
    const message = error.response?.data?.detail || 'Request failed';
    throw new Error(message);
  }
}
```

### New React Component
```typescript
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ComponentNameProps {
  data: DataType;
  onAction: (id: string) => void;
}

export const ComponentName: React.FC<ComponentNameProps> = ({ data, onAction }) => {
  const [loading, setLoading] = useState(false);

  const handleAction = async () => {
    setLoading(true);
    try {
      await onAction(data.id);
    } catch (error) {
      console.error('Action failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{data.name}</CardTitle>
      </CardHeader>
      <CardContent>
        <Button onClick={handleAction} disabled={loading}>
          {loading ? 'Processing...' : 'Action'}
        </Button>
      </CardContent>
    </Card>
  );
};
```

### Form Component with Validation
```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';

const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email format'),
});

type FormValues = z.infer<typeof formSchema>;

export const FormComponent: React.FC = () => {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: { name: '', email: '' },
  });

  const onSubmit = async (values: FormValues) => {
    try {
      // API call here
      console.log('Form values:', values);
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
};
```

### Transaction Filtering Pattern
```typescript
// Advanced filtering with AI recommendations
import { matchesRecommendationFilter, matchesConfidenceFilter } from '@/constants/transaction-filters';

const filteredTransactions = useMemo(() => {
  let filtered = allTransactions;

  // Apply search filter
  if (searchTerm) {
    const searchLower = searchTerm.toLowerCase();
    filtered = filtered.filter(transaction =>
      transaction.metadata?.contact_name?.toLowerCase().includes(searchLower) ||
      transaction.document_number?.toLowerCase().includes(searchLower)
    );
  }

  // Apply advanced filters
  if (selectedAdvancedFilters.length > 0) {
    const recommendationFilters = selectedAdvancedFilters.filter(f => 
      ['create_prepayment', 'pending_analysis', 'no_prepayment', 'analysis_failed', 'skipped'].includes(f)
    );
    const confidenceFilters = selectedAdvancedFilters.filter(f => 
      ['high', 'medium', 'low'].includes(f)
    );

    filtered = filtered.filter(transaction => {
      return matchesRecommendationFilter(transaction, recommendationFilters) &&
             matchesConfidenceFilter(transaction, confidenceFilters);
    });
  }

  return filtered;
}, [allTransactions, searchTerm, selectedAdvancedFilters]);
```

### Data Fetching Hook
```typescript
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

export const useDataFetch = <T>(
  fetchFunction: () => Promise<T>,
  dependencies: any[] = []
) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await fetchFunction();
      setData(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, dependencies);

  return { data, loading, error, refetch: fetchData };
};
```

### Error Boundary Component
```typescript
import React, { Component, ReactNode } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <Card>
          <CardHeader>
            <CardTitle>Something went wrong</CardTitle>
          </CardHeader>
          <CardContent>
            <p>An error occurred while rendering this component.</p>
            <Button onClick={() => this.setState({ hasError: false })}>
              Try again
            </Button>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}
```

## TypeScript Interface Templates

### API Response Types
```typescript
// Standard API response wrapper
interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// Pagination response
interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    per_page: number;
    pages: number;
  };
}

// Client token usage response with credit tracking
interface ClientTokenUsageResponse {
  client_id: string;
  total_openai_tokens: number;
  total_mistral_tokens: number;
  total_cost: number;
  total_operations: number;
  sync_jobs_count: number;
  start_date?: string;
  end_date?: string;
  sync_jobs: Array<{
    sync_job_id: string;
    entity_id?: string;
    status?: string;
    openai_tokens: number;
    mistral_tokens: number;
    total_cost: number;
    operations_count: number;
    started_at?: string;
    completed_at?: string;
    // Credit usage tracking for user-facing billing
    credit_usage?: {
      credits_used: number;
      credits_used_openai: number;
      credits_used_mistral: number;
    };
  }>;
}

// Entity response (matches backend)
interface EntityResponse {
  entity_id: string;
  entity_name: string;
  type: 'xero' | 'qbo' | 'manual';
  status: 'active' | 'inactive' | 'error';
  connection_details: {
    status: 'active' | 'error' | 'disconnected';
    xero_tenant_id?: string;
    last_sync?: string;
    error_message?: string;
  };
}

// Transaction with AI recommendations
interface TransactionWithAI {
  id: string;
  document_number: string;
  total: number;
  amount_due: number;
  amount_paid: number;
  currency: string;
  status: string;
  has_amortization_schedules?: boolean;
  skip_reason?: string;
  metadata: {
    contact_name: string;
    recommended_action?: 'create_prepayment' | 'pending_analysis' | 'no_prepayment' | 'analysis_failed';
    confidence_score?: number; // 0-1 scale
    has_prepayment_line_items?: boolean;
    gl_based_analysis_completed?: boolean;
    llm_based_analysis_completed?: boolean;
    skip_reason?: string;
  };
  line_items: LineItem[];
}

// Schedule response
interface ScheduleResponse {
  schedule_id: string;
  transaction_id: string;
  status: 'pending_configuration' | 'pending_confirmation' | 'confirmed' | 'posted' | 'cancelled';
  original_amount: number;
  monthly_entries: MonthlyEntry[];
  amortization_start_date: string;
  amortization_end_date: string;
}

// Monthly entry
interface MonthlyEntry {
  entry_index: number;
  amount: number;
  date: string;
  posted: boolean;
  xero_journal_id?: string;
}
```

### Form Types
```typescript
// Client creation form
interface ClientCreateForm {
  name: string;
  client_type: string;
  industry?: string;
  contact_email?: string;
  contact_phone?: string;
}

// Entity settings form
interface EntitySettingsForm {
  entity_name: string;
  prepayment_asset_account_codes: string[];
  default_expense_account_code?: string;
  amortization_materiality_threshold?: number;
  auto_sync_enabled: boolean;
  sync_frequency: 'hourly' | 'daily' | 'weekly' | 'manual';
}
```

## Quick Debug Commands

### Browser Console Debug
```javascript
// Check Firebase auth token
localStorage.getItem('firebase:authUser:drcr-d660a:[DEFAULT]')

// Test API connectivity
fetch('http://localhost:8081/health')
  .then(r => r.json())
  .then(console.log)

// Check current user
const token = 'your-firebase-id-token';
fetch('http://localhost:8081/auth/me', {
  headers: { 'Authorization': `Bearer ${token}` }
})
.then(r => r.json())
.then(console.log)
```

### Common Debug Issues
- **401 Unauthorized**: Check Firebase token expiry, refresh if needed
- **CORS errors**: Verify backend is running on port 8081
- **Network failures**: Check if backend server is running
- **State issues**: Use React DevTools to inspect component state
- **Form validation**: Check Zod schema matches API requirements

### Error Handling Pattern
```typescript
const handleApiError = (error: any): string => {
  if (error.response?.status === 401) {
    // Redirect to login
    window.location.href = '/login';
    return 'Please log in again';
  }
  if (error.response?.status === 403) {
    return 'Access denied';
  }
  return error.response?.data?.detail || 'Something went wrong';
};
```

## Documentation References

**For detailed information, see:**
- **Complete API Reference:** `docs/API_REFERENCE.md`
- **Service Layer Guide:** `docs/SERVICES_GUIDE.md`
- **Component Architecture:** `docs/COMPONENT_ARCHITECTURE.md`
- **Troubleshooting:** `docs/TROUBLESHOOTING.md`
- **Development Workflows:** `docs/DEVELOPMENT_WORKFLOWS.md`

## Development Commands
```bash
npm run dev        # Start development server
npm run build      # Build for production
npm run lint       # Run linter
npm test          # Run tests
```

## Critical Notes
- **Never hardcode data** - Always fetch from APIs
- **Use service layer** - Don't call API directly from components
- **Follow TypeScript** - All code should be properly typed
- **Error handling** - Always handle API errors gracefully
- **Loading states** - Show loading for all async operations

## 📝 Documentation Update Rules
**After ANY code change, you MUST update documentation:**
1. **New API endpoint** → Update `docs/API_REFERENCE.md` + add to CLAUDE.md if commonly used
2. **New component** → Update `docs/COMPONENT_ARCHITECTURE.md` + add pattern to CLAUDE.md if reusable
3. **New service method** → Update `docs/SERVICES_GUIDE.md` + add template to CLAUDE.md if common
4. **Data structure change** → Update TypeScript interfaces in CLAUDE.md + relevant docs
5. **Integration change** → Update `docs/BACKEND_INTEGRATION.md` + cross-project docs

**Validation**: Check `/mnt/d/Projects/DOCUMENTATION_UPDATE_PROCESS.md` for full process

---
**Last Updated:** 2025-07-17 | **Lines:** ~410 (AI-optimized with templates) | **Backend:** `/mnt/d/Projects/drcr_back/CLAUDE.md`

## Recent Critical Updates

### Custom Narration for Xero Journal Entries (2025-07-22)
**Feature**: Added ability to enter/edit custom narration in Bills for Amortization
**Implementation**: 
- **Component**: Added narration textarea to `AmortizationConfiguration.tsx` 
- **Interface**: Updated `AmortizationConfig` to include `custom_narration?: string`
- **API Integration**: Added field to `PrepaymentsService.recalculateSchedule()`
```typescript
// Frontend usage in AmortizationConfiguration
const handleNarrationChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
  onChange({ custom_narration: event.target.value });
};

<Textarea
  value={config.custom_narration || ''}
  onChange={handleNarrationChange}
  placeholder="Custom narration for journal entries (leave blank for auto-generated)"
  maxLength={500}
/>
```
**User Experience**: 500-char limit with counter, helpful placeholder, auto-generated fallback explanation
**Location**: Bills for Amortization → Configuration section → "Narration (Optional)" field

### Credit Usage Display Update (2025-07-17)
**Changed**: BillingPage Usage History simplified to show **credit usage only**
**Impact**: 
- Usage History table shows: Date, Entity, Credits Used, Status
- Removed technical LLM provider columns (OpenAI/Mistral)
- Updated `ClientTokenUsageResponse` interface to include `credit_usage` field
- Added sync job status badges for completion tracking
- Aligns with backend credit tracking implementation
- Provides clean, user-facing billing information (1 credit = 1 page processed)