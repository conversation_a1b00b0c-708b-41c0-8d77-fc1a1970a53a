from fastapi import APIRouter, Depends, HTTPException, Path, Query, Body, status
from typing import List, Optional, Dict, Any
from google.cloud.firestore import SERVER_TIMESTAMP
import uuid
import os
from datetime import datetime

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db, get_transaction_service
from ..services.transaction_service import TransactionService
from ..services.amortization_service import AmortizationService
from ..utils.narration_helper import build_narration
from ..schemas.schedule_schemas import (
    SchedulePreviewRequest, SchedulePreviewResponse, 
    ScheduleModificationPreviewRequest, NewSchedulePreviewRequest,
    ScheduleUpdateRequest, ScheduleUpdateResponse,
    MonthlyEntryUpdateRequest, MonthlyEntryUpdateResponse,
    ScheduleCreateRequest, ScheduleCreateResponse
)
from drcr_shared_logic.clients.xero_client import XeroApiClient

router = APIRouter(tags=["Amortization Schedules"])

@router.post("/calculate-preview", response_model=SchedulePreviewResponse)
async def calculate_schedule_preview(
    request: SchedulePreviewRequest = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Calculate amortization schedule preview without saving to database.
    
    This endpoint allows the frontend to show real-time calculation previews
    as users modify schedule parameters.
    """
    try:
        # Get entity settings for materiality threshold and 50% rule
        entity_ref = db.collection("ENTITIES").document(request.entity_id)
        entity_doc = await entity_ref.get()
        
        if not entity_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Entity {request.entity_id} not found"
            )
        
        # Load settings from ENTITY_SETTINGS collection
        settings_ref = db.collection("ENTITY_SETTINGS").document(request.entity_id)
        settings_doc = await settings_ref.get()
        
        if settings_doc.exists:
            entity_settings = settings_doc.to_dict()
        else:
            # Fallback to entity.settings if ENTITY_SETTINGS doesn't exist
            entity_data = entity_doc.to_dict()
            entity_settings = entity_data.get("settings", {})
        
        # Verify user has access to this entity
        # This check depends on your access control model
        # For now, just ensure the user is authenticated
        
        # Calculate the preview using AmortizationService
        amortization_service = AmortizationService()
        preview_result = amortization_service.calculate_preview(
            amount=request.amount,
            start_date=request.start_date,
            end_date=request.end_date,
            entity_settings=entity_settings,
            force_calculation_method=request.calculation_method.value if request.calculation_method != "auto" else None
        )
        
        return SchedulePreviewResponse(**preview_result)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate schedule preview: {str(e)}"
        )

@router.post("/", response_model=ScheduleCreateResponse)
async def create_schedule(
    request: ScheduleCreateRequest = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Create a new amortization schedule with user-edited preview data.
    
    This endpoint saves the schedule with exactly the monthly entries provided,
    preserving any manual edits the user made to the preview data.
    """
    try:
        # Validate transaction exists and get details
        transaction = await transaction_service.get_transaction_by_id(request.transaction_id)
        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Transaction {request.transaction_id} not found"
            )
        
        # Check if user has access to this client
        await get_firm_user_with_client_access(transaction.client_id, current_user)
        
        # Get entity_id from transaction for later use
        entity_id = transaction.entity_id
        
        # Generate unique schedule ID
        schedule_id = str(uuid.uuid4())
        
        # Convert monthly entries to database format
        monthly_entries = []
        for entry in request.monthly_entries:
            # Convert date string to datetime if needed
            month_date = entry.month_date
            if isinstance(month_date, str):
                month_date = datetime.fromisoformat(month_date).replace(tzinfo=None)
            
            monthly_entries.append({
                "monthDate": month_date,
                "amount": entry.amount,
                "status": "proposed",
                "postedJournalId": None,
                "postedJournalLineId": None,
                "matchConfidence": None,
                "lastActionByUserId": current_user.uid,
                "lastActionTimestamp": datetime.now().isoformat(),
                "postingError": None,
            })
        
        # Determine initial status based on account codes
        initial_status = "pending_configuration"
        if request.account_code and request.expense_account_code:
            initial_status = "proposed"
        
        # Create schedule document
        schedule_data = {
            "schedule_id": schedule_id,
            "transaction_id": request.transaction_id,
            "client_id": transaction.client_id,
            "entity_id": entity_id,
            "status": initial_status,
            "originalAmount": request.amount,
            "amortizationStartDate": datetime.combine(request.start_date, datetime.min.time()),
            "amortizationEndDate": datetime.combine(request.end_date, datetime.min.time()),
            "calculation_method": request.calculation_method,
            "detection_method": "manual",  # Manually created
            "numberOfPeriods": len(monthly_entries),
            "periodType": "monthly",
            "monthlyEntries": monthly_entries,
            "description": request.description or f"Manual amortization schedule",
            "created_at": SERVER_TIMESTAMP,
            "updated_at": SERVER_TIMESTAMP,
            "lastActionByUserId": current_user.uid,
            "lastActionTimestamp": datetime.now().isoformat(),
            "is_llm_detected": False
        }
        
        # Add account codes if provided
        if request.account_code:
            schedule_data["amortizationAccountCode"] = request.account_code
        if request.expense_account_code:
            schedule_data["expenseAccountCode"] = request.expense_account_code
        
        # Save to Firestore
        schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
        await schedule_ref.set(schedule_data)
        
        # Create audit log entry
        await _create_audit_log_entry(
            db=db,
            event_category="AMORTIZATION_MANAGEMENT",
            event_type="SCHEDULE_CREATED",
            client_id=transaction.client_id,
            entity_id=entity_id,
            transaction_id=request.transaction_id,
            schedule_id=schedule_id,
            status="SUCCESS",
            details={
                "amount": request.amount,
                "start_date": request.start_date.isoformat(),
                "end_date": request.end_date.isoformat(),
                "calculation_method": request.calculation_method,
                "total_months": len(monthly_entries),
                "initial_status": initial_status,
                "user_email": current_user.email,
                "creation_method": "manual"
            },
            user_id=current_user.uid
        )
        
        # Prepare response data
        schedule_data["id"] = schedule_id  # Add id field for response
        
        return ScheduleCreateResponse(
            success=True,
            message="Schedule created successfully",
            schedule_id=schedule_id,
            schedule=schedule_data
        )
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create schedule: {str(e)}"
        )

async def _create_audit_log_entry(
    db,
    event_category: str,
    event_type: str,
    client_id: str,
    entity_id: str,
    transaction_id: str,
    schedule_id: str,
    status: str,
    details: Dict[str, Any],
    user_id: Optional[str] = None
):
    """Creates an audit log entry in Firestore."""
    try:
        log_entry_id = str(uuid.uuid4())
        log_data = {
            "timestamp": SERVER_TIMESTAMP,
            "event_category": event_category,
            "event_type": event_type,
            "client_id": client_id,
            "entity_id": entity_id,
            "transaction_id": transaction_id,
            "schedule_id": schedule_id,
            "user_id": user_id,
            "status": status,
            "details": details
        }

        # Remove None fields
        log_data = {k: v for k, v in log_data.items() if v is not None}

        audit_log_ref = db.collection("AUDIT_LOG").document(log_entry_id)
        await audit_log_ref.set(log_data)
    except Exception as e:
        # Log error but don't fail the main operation
        print(f"Failed to create audit log: {e}")

@router.get("/{schedule_id}")
async def get_schedule(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """Get detailed information about an amortization schedule"""
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("client_id") or schedule_data.get("clientId")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Get attachment information for the related transaction
    transaction_id = schedule_data.get("transaction_id") or schedule_data.get("transactionId")
    if transaction_id:
        try:
            attachments = await transaction_service.get_transaction_attachments(transaction_id)
            schedule_data["has_attachments"] = len(attachments) > 0
            schedule_data["attachments"] = [
                {
                    "id": att.id,
                    "file_name": att.file_name,
                    "content_type": att.content_type,
                    "size_bytes": att.size_bytes,
                    "download_url": att.download_url
                } for att in attachments
            ]
            if attachments:
                schedule_data["attachment_id"] = attachments[0].id  # Primary attachment
        except Exception as e:
            # Log error but don't fail the request
            print(f"Error fetching attachments for schedule {schedule_id}: {e}")
            schedule_data["has_attachments"] = False
            schedule_data["attachments"] = []
    else:
        schedule_data["has_attachments"] = False
        schedule_data["attachments"] = []
    
    # Return schedule data
    schedule_data["schedule_id"] = schedule_id
    return schedule_data

@router.get("/{schedule_id}/attachments")
async def get_schedule_attachments(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """Get attachments for an amortization schedule"""
    # Get schedule record to find the transaction_id
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("client_id") or schedule_data.get("clientId")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Get the transaction_id from the schedule
    transaction_id = schedule_data.get("transaction_id") or schedule_data.get("transactionId")
    if not transaction_id:
        return []  # No transaction linked, no attachments
    
    # Get attachments from the linked transaction
    attachments = await transaction_service.get_transaction_attachments(transaction_id)
    return attachments

@router.put("/{schedule_id}")
async def update_schedule(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    schedule_data: Dict[str, Any] = Body(..., description="Updated schedule data"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Update an amortization schedule (dates, amounts, account codes)"""
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()

    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")

    existing_schedule = schedule_doc.to_dict()
    client_id = existing_schedule.get("clientId") or existing_schedule.get("client_id")
    entity_id = existing_schedule.get("entity_id") or existing_schedule.get("entityId")
    transaction_id = existing_schedule.get("transaction_id") or existing_schedule.get("transactionId")
    current_status = existing_schedule.get("status")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Map API field names to database field names
    field_mapping = {
        "account_code": "amortizationAccountCode",
        "expense_account_code": "expenseAccountCode", 
        "amount": "originalAmount",
        "start_date": "amortizationStartDate",
        "end_date": "amortizationEndDate",
        "description": "notes",
        "monthly_entries": "monthlyEntries",
        # Also accept database field names directly
        "amortizationAccountCode": "amortizationAccountCode",
        "expenseAccountCode": "expenseAccountCode",
        "originalAmount": "originalAmount",
        "amortizationStartDate": "amortizationStartDate", 
        "amortizationEndDate": "amortizationEndDate",
        "notes": "notes",
        "monthlyEntries": "monthlyEntries",
        "custom_narration": "custom_narration",  # accept canonical snake_case
        "customNarration": "custom_narration",
        "narration": "custom_narration",  # legacy fallback
    }

    # Transform incoming data to database field names
    update_data = {}
    for key, value in schedule_data.items():
        if key in field_mapping:
            db_field = field_mapping[key]
            update_data[db_field] = value

    if not update_data:
        raise HTTPException(status_code=400, detail="No valid fields to update")

    # Merge update data with existing schedule to check for status progression
    merged_data = {**existing_schedule, **update_data}

    # Determine if status should be automatically advanced
    new_status = _determine_automatic_status_progression(current_status, merged_data)

    # Add status update if progression occurred
    if new_status != current_status:
        update_data["status"] = new_status
        update_data[f"{new_status}_at"] = SERVER_TIMESTAMP
        update_data[f"{new_status}_by"] = current_user.uid

    # Add metadata
    update_data["updated_at"] = SERVER_TIMESTAMP
    update_data["updated_by"] = current_user.uid

    # Update the schedule
    await schedule_ref.update(update_data)

    # Create audit log entry
    audit_details = {
        "updated_fields": list(update_data.keys()),
        "user_email": current_user.email
    }

    # Add status progression info if it occurred
    if new_status != current_status:
        audit_details["status_progression"] = {
            "from": current_status,
            "to": new_status,
            "reason": "automatic_field_completion"
        }

    await _create_audit_log_entry(
        db=db,
        event_category="AMORTIZATION_MANAGEMENT",
        event_type="SCHEDULE_UPDATED",
        client_id=client_id,
        entity_id=entity_id,
        transaction_id=transaction_id,
        schedule_id=schedule_id,
        status="SUCCESS",
        details=audit_details,
        user_id=current_user.uid
    )

    response_data = {"message": "Schedule updated successfully", "schedule_id": schedule_id}

    # Include status progression info in response
    if new_status != current_status:
        response_data["status_progression"] = {
            "from": current_status,
            "to": new_status
        }

    return response_data

@router.put("/{schedule_id}/recalculate", response_model=ScheduleUpdateResponse)
async def recalculate_and_update_schedule(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    request: ScheduleUpdateRequest = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Recalculate and update an amortization schedule with new parameters.
    
    This endpoint recalculates the schedule using the AmortizationService
    and updates the database with the new monthly entries.
    """
    try:
        # Get existing schedule record
        schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
        schedule_doc = await schedule_ref.get()
        
        if not schedule_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Amortization schedule not found"
            )
        
        existing_schedule = schedule_doc.to_dict()
        client_id = existing_schedule.get("client_id")
        entity_id = existing_schedule.get("entity_id")
        current_status = existing_schedule.get("status")
        
        # Check if user has access to this client
        await get_firm_user_with_client_access(client_id, current_user)
        
        # Only allow recalculation for schedules that haven't been confirmed yet
        if current_status not in ["proposed", "pending_configuration"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot recalculate schedule with status: {current_status}"
            )
        
        # Get entity settings for materiality threshold
        entity_ref = db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()
        
        if not entity_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Entity {entity_id} not found"
            )
        
        entity_data = entity_doc.to_dict()
        entity_settings = entity_data.get("settings", {})
        
        # Calculate new schedule using AmortizationService
        amortization_service = AmortizationService()
        schedule_preview = amortization_service.calculate_preview(
            amount=request.amount,
            start_date=request.start_date,
            end_date=request.end_date,
            entity_settings=entity_settings
        )
        
        # Convert preview format to database format
        monthly_entries = []
        for entry in schedule_preview['monthly_entries']:
            month_date_str = entry['month_date']
            month_date = datetime.fromisoformat(month_date_str).replace(tzinfo=None)
            
            monthly_entries.append({
                "monthDate": month_date,
                "amount": entry['amount'],
                "status": "proposed",  # Reset to proposed status
                "postedJournalId": None,
                "postedJournalLineId": None,
                "matchConfidence": None,
                "lastActionByUserId": None,
                "lastActionTimestamp": None,
                "postingError": None,
            })
        
        # Update the schedule in database
        update_data = {
            "originalAmount": request.amount,
            "amortizationStartDate": datetime.combine(request.start_date, datetime.min.time()).replace(tzinfo=None),
            "amortizationEndDate": datetime.combine(request.end_date, datetime.min.time()).replace(tzinfo=None),
            "numberOfPeriods": schedule_preview['total_months'],
            "monthlyEntries": monthly_entries,
            "calculationMethod": schedule_preview['calculation_method'],
            "materialityThreshold": schedule_preview['materiality_threshold'],
            # Accept custom narration under any casing sent by the UI
            "custom_narration": request.custom_narration
                if getattr(request, 'custom_narration', None) is not None
                else getattr(request, 'customNarration', None)
                if hasattr(request, 'customNarration') else getattr(request, 'narration', None),
            "lastModifiedAt": SERVER_TIMESTAMP,
            "lastModifiedByUserId": current_user.user_id,
            "status": "proposed"  # Reset status to proposed
        }
        
        await schedule_ref.update(update_data)
        
        # Create audit log entry
        await _create_audit_log_entry(
            db=db,
            event_category="schedule_management",
            event_type="schedule_recalculated",
            client_id=client_id,
            entity_id=entity_id,
            transaction_id=existing_schedule.get("transaction_id"),
            schedule_id=schedule_id,
            status="success",
            details={
                "new_amount": request.amount,
                "new_start_date": request.start_date.isoformat(),
                "new_end_date": request.end_date.isoformat(),
                "calculation_method": schedule_preview['calculation_method'],
                "total_months": schedule_preview['total_months']
            },
            user_id=current_user.user_id
        )
        
        return ScheduleUpdateResponse(
            success=True,
            message="Schedule recalculated and updated successfully",
            schedule_id=schedule_id,
            updated_entries_count=len(monthly_entries)
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to recalculate schedule: {str(e)}"
        )

@router.put("/{schedule_id}/preview-changes", response_model=SchedulePreviewResponse)
async def preview_schedule_changes(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    request: ScheduleModificationPreviewRequest = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Preview changes to an existing amortization schedule without saving.
    
    This endpoint allows users to see how modifications to an existing schedule
    would affect the monthly entries before actually saving the changes.
    """
    try:
        # Get existing schedule record
        schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
        schedule_doc = await schedule_ref.get()
        
        if not schedule_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Amortization schedule not found"
            )
        
        existing_schedule = schedule_doc.to_dict()
        client_id = existing_schedule.get("client_id")
        entity_id = existing_schedule.get("entity_id")
        current_status = existing_schedule.get("status")
        
        # Check if user has access to this client
        await get_firm_user_with_client_access(client_id, current_user)
        
        # Allow preview for any schedule (even confirmed ones) since it's read-only
        # This lets users explore "what if" scenarios
        
        # Get entity settings for materiality threshold
        entity_ref = db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()
        
        if not entity_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Entity {entity_id} not found"
            )
        
        entity_data = entity_doc.to_dict()
        entity_settings = entity_data.get("settings", {})
        
        # Calculate preview using AmortizationService
        amortization_service = AmortizationService()
        preview_result = amortization_service.calculate_preview(
            amount=request.amount,
            start_date=request.start_date,
            end_date=request.end_date,
            entity_settings=entity_settings,
            force_calculation_method=request.calculation_method.value if request.calculation_method != "auto" else None
        )
        
        return SchedulePreviewResponse(**preview_result)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to preview schedule changes: {str(e)}"
        )

@router.post("/{schedule_id}/confirm")
async def confirm_schedule(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """Confirm an amortization schedule and post journals to Xero"""
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("client_id")
    entity_id = schedule_data.get("entity_id")
    transaction_id = schedule_data.get("transaction_id")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Validate that required fields are set before confirmation
    expense_account_code = schedule_data.get("expenseAccountCode")
    amortization_account_code = schedule_data.get("amortizationAccountCode")
    
    if not expense_account_code:
        raise HTTPException(
            status_code=400,
            detail="Cannot confirm schedule: Expense account code must be set before confirmation"
        )
    
    if not amortization_account_code:
        raise HTTPException(
            status_code=400,
            detail="Cannot confirm schedule: Amortization account code must be set before confirmation"
        )
    
    # Check if schedule is in a state that can be confirmed
    current_status = schedule_data.get("status")
    if current_status not in ["proposed", "pending_configuration"]:
        raise HTTPException(
            status_code=400,
            detail=f"Schedule cannot be confirmed from current status: {current_status}"
        )
    
    # Update monthly entries status to match schedule status
    # Use current timestamp instead of SERVER_TIMESTAMP for nested objects
    current_timestamp = datetime.utcnow()
    monthly_entries = schedule_data.get("monthlyEntries", [])
    updated_monthly_entries = []
    for entry in monthly_entries:
        updated_entry = entry.copy()
        updated_entry["status"] = "due_for_posting"
        updated_entry["lastActionByUserId"] = current_user.uid
        updated_entry["lastActionTimestamp"] = current_timestamp
        updated_monthly_entries.append(updated_entry)
    
    # Update schedule status and monthly entries
    await schedule_ref.update({
        "status": "confirmed",
        "confirmed_at": SERVER_TIMESTAMP,
        "confirmed_by": current_user.uid,
        "updated_at": SERVER_TIMESTAMP,
        "monthlyEntries": updated_monthly_entries
    })
    
    # Initialize XeroApiClient (uses auto-configuration)
    xero_client = await XeroApiClient.create(
        platform_org_id=entity_id,
        tenant_id=client_id
    )
    
    # Get transaction details for narration
    transaction_details = None
    supplier_name = "Unknown Supplier"
    invoice_ref = schedule_data.get('transactionReference', 'Invoice')
    invoice_date = ""
    
    if transaction_id:
        try:
            transaction_details = await transaction_service.get_transaction_by_id(transaction_id)
            if transaction_details:
                # Extract supplier name - it's in metadata, added by transformer
                if transaction_details.metadata and 'contact_name' in transaction_details.metadata:
                    supplier_name = transaction_details.metadata['contact_name']
                
                # Get invoice reference and date
                if transaction_details.document_number:
                    invoice_ref = transaction_details.document_number
                if transaction_details.date_issued:
                    invoice_date = transaction_details.date_issued.strftime('%d/%m/%Y')
        except Exception as e:
            print(f"Error getting transaction details for narration: {e}")
    
    # Process monthly entries that need to be posted
    monthly_entries = updated_monthly_entries
    success_count = 0
    error_count = 0
    
    for i, entry in enumerate(monthly_entries):
        entry_status = entry.get("status")
        
        # Only post entries that are due for posting
        if entry_status == "due_for_posting":
            try:
                # Prepare journal data
                journal_date = entry.get("monthDate")
                journal_amount = entry.get("amount")
                
                # Build narration using helper (custom or auto-generated)
                entry_context = {
                    "transaction_details": transaction_details,
                    "entry_index": i,
                    "total_entries": len(monthly_entries)
                }
                narration = build_narration(schedule_data, entry_context)
                
                # Create journal data for XeroApiClient
                journal_data = {
                    "narration": narration,
                    "journalDate": journal_date,  # Let XeroApiClient handle date formatting
                    "status": "DRAFT",
                    "lines": [
                        {
                            "accountCode": schedule_data.get("expenseAccountCode"),
                            "description": "Amortization expense",
                            "amount": journal_amount,
                            "isDebit": True,
                            "isCredit": False
                        },
                        {
                            "accountCode": schedule_data.get("amortizationAccountCode"),
                            "description": "Prepaid asset reduction",
                            "amount": journal_amount,
                            "isDebit": False,
                            "isCredit": True
                        }
                    ]
                }
                journal_result = await xero_client.create_manual_journal(journal_data=journal_data)
                
                # Update entry with posting details
                if journal_result and journal_result.get("ManualJournals") and not journal_result.get("error"):
                    xero_journal = journal_result["ManualJournals"][0]
                    monthly_entries[i]["status"] = "posted"
                    monthly_entries[i]["posted_at"] = datetime.now().isoformat()
                    monthly_entries[i]["xero_journal_id"] = xero_journal.get("ManualJournalID")
                    success_count += 1
                else:
                    # Handle Xero API error
                    error_msg = journal_result.get("error", "Unknown Xero API error") if journal_result else "No response from Xero API"
                    monthly_entries[i]["status"] = "posting_failed"
                    monthly_entries[i]["error"] = str(error_msg)
                    error_count += 1
                
            except Exception as e:
                # Update entry with error details
                monthly_entries[i]["status"] = "posting_failed"
                monthly_entries[i]["error"] = str(e)
                error_count += 1
    
    # Update schedule with processed entries
    await schedule_ref.update({
        "monthlyEntries": monthly_entries,
        "updated_at": SERVER_TIMESTAMP
    })
    
    # Create audit log entry
    await _create_audit_log_entry(
        db=db,
        event_category="AMORTIZATION_MANAGEMENT",
        event_type="SCHEDULE_CONFIRMED",
        client_id=client_id,
        entity_id=entity_id,
        transaction_id=transaction_id,
        schedule_id=schedule_id,
        status="SUCCESS",
        details={
            "entries_posted": success_count,
            "entries_failed": error_count,
            "user_email": current_user.email
        },
        user_id=current_user.uid
    )
    
    return {
        "message": "Schedule confirmed successfully",
        "schedule_id": schedule_id,
        "entries_posted": success_count,
        "entries_failed": error_count
    }

@router.post("/{schedule_id}/skip")
async def skip_schedule(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    reason: str = Body(..., embed=True, description="Reason for skipping"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Skip an amortization schedule"""
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("clientId") or schedule_data.get("client_id")
    entity_id = schedule_data.get("entityId") or schedule_data.get("entity_id")
    transaction_id = schedule_data.get("transactionId") or schedule_data.get("transaction_id")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Check if schedule is in a state that can be skipped
    current_status = schedule_data.get("status")
    if current_status not in ["proposed", "pending_configuration"]:
        raise HTTPException(
            status_code=400,
            detail=f"Schedule cannot be skipped from current status: {current_status}"
        )
    
    # Update monthly entries status to match schedule status
    # Use current timestamp instead of SERVER_TIMESTAMP for nested objects
    current_timestamp = datetime.utcnow().isoformat()
    monthly_entries = schedule_data.get("monthlyEntries", [])
    updated_monthly_entries = []
    for entry in monthly_entries:
        updated_entry = entry.copy()
        updated_entry["status"] = "skipped"
        updated_entry["lastActionByUserId"] = current_user.uid
        updated_entry["lastActionTimestamp"] = current_timestamp
        updated_monthly_entries.append(updated_entry)
    
    # Update schedule status and monthly entries
    await schedule_ref.update({
        "status": "skipped",
        "skipped_at": SERVER_TIMESTAMP,
        "skipped_by": current_user.uid,
        "skip_reason": reason,
        "updated_at": SERVER_TIMESTAMP,
        "monthlyEntries": updated_monthly_entries
    })
    
    # Create audit log entry
    await _create_audit_log_entry(
        db=db,
        event_category="AMORTIZATION_MANAGEMENT",
        event_type="SCHEDULE_SKIPPED",
        client_id=client_id,
        entity_id=entity_id,
        transaction_id=transaction_id,
        schedule_id=schedule_id,
        status="SUCCESS",
        details={
            "reason": reason,
            "user_email": current_user.email
        },
        user_id=current_user.uid
    )
    
    return {"message": "Schedule skipped successfully", "schedule_id": schedule_id}

@router.put("/{schedule_id}/status")
async def update_schedule_status(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    status_data: Dict[str, Any] = Body(..., description="Status update data"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Update the status of an amortization schedule"""
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("clientId") or schedule_data.get("client_id")
    entity_id = schedule_data.get("entityId") or schedule_data.get("entity_id")
    transaction_id = schedule_data.get("transactionId") or schedule_data.get("transaction_id")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Get new status
    new_status = status_data.get("status")
    if not new_status:
        raise HTTPException(status_code=400, detail="Status is required")
    
    # Validate status
    valid_statuses = [
        "pending_configuration",
        "proposed", 
 
        "confirmed", 
        "posted",
        "skipped", 
        "cancelled",
        "error",
        "due_for_posting",
        "posting_failed",
        "journal_proposed"
    ]
    
    if new_status not in valid_statuses:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
        )
    
    # Update monthly entries status to match schedule status
    # Use current timestamp instead of SERVER_TIMESTAMP for nested objects
    current_timestamp = datetime.utcnow().isoformat()
    monthly_entries = schedule_data.get("monthlyEntries", [])
    updated_monthly_entries = []
    for entry in monthly_entries:
        updated_entry = entry.copy()
        updated_entry["status"] = new_status
        updated_entry["lastActionByUserId"] = current_user.uid
        updated_entry["lastActionTimestamp"] = current_timestamp
        updated_monthly_entries.append(updated_entry)
    
    # Update status
    update_data = {
        "status": new_status,
        f"{new_status}_at": SERVER_TIMESTAMP,
        f"{new_status}_by": current_user.uid,
        "updated_at": SERVER_TIMESTAMP,
        "monthlyEntries": updated_monthly_entries
    }
    
    # Add notes if provided
    if "notes" in status_data:
        update_data["notes"] = status_data["notes"]
    
    await schedule_ref.update(update_data)
    
    # Create audit log entry
    await _create_audit_log_entry(
        db=db,
        event_category="AMORTIZATION_MANAGEMENT",
        event_type="SCHEDULE_STATUS_UPDATED",
        client_id=client_id,
        entity_id=entity_id,
        transaction_id=transaction_id,
        schedule_id=schedule_id,
        status="SUCCESS",
        details={
            "previous_status": schedule_data.get("status"),
            "new_status": new_status,
            "user_email": current_user.email
        },
        user_id=current_user.uid
    )
    
    return {"message": f"Schedule status updated to {new_status}", "schedule_id": schedule_id}


def _determine_automatic_status_progression(current_status: str, schedule_data: Dict[str, Any]) -> str:
    """
    Determine if a schedule should automatically advance to the next status based on field completion.

    Status Progression Rules:
    - PENDING_CONFIGURATION → PROPOSED: When amortizationAccountCode is set
    - PROPOSED → CONFIRMED: When user approves the schedule (manual action)

    Args:
        current_status: Current status of the schedule
        schedule_data: Complete schedule data after merging updates

    Returns:
        New status if progression should occur, otherwise current status
    """
    # Only progress from specific statuses
    if current_status not in ["pending_configuration", "proposed"]:
        return current_status

    # Get required fields
    amortization_account = schedule_data.get("amortizationAccountCode")
    expense_account = schedule_data.get("expenseAccountCode")

    # Check for progression from PENDING_CONFIGURATION
    if current_status == "pending_configuration":
        if amortization_account:
            # Advance to PROPOSED when amortization account is set
            return "proposed"

    # PROPOSED requires manual user action to move to CONFIRMED
    # No automatic progression from PROPOSED

    # No progression needed
    return current_status


@router.post("/{schedule_id}/entries/{entry_index}/post")
async def post_individual_entry(
    schedule_id: str = Path(..., description="The amortization schedule ID"),
    entry_index: int = Path(..., description="Index of the monthly entry to post (0-based)"),
    current_user: AuthUser = Depends(get_current_user),
    db=Depends(get_db),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """Posts an individual monthly entry from an amortization schedule to Xero."""
    
    # Get the schedule
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("client_id")
    entity_id = schedule_data.get("entity_id")
    transaction_id = schedule_data.get("transaction_id")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Get monthly entries
    monthly_entries = schedule_data.get("monthlyEntries", [])
    
    if entry_index < 0 or entry_index >= len(monthly_entries):
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid entry index. Must be between 0 and {len(monthly_entries)-1}"
        )
    
    entry = monthly_entries[entry_index]
    entry_status = entry.get("status")
    
    # Check if entry can be posted
    if entry_status not in ["proposed", "due_for_posting", "posting_failed"]:
        raise HTTPException(
            status_code=400,
            detail=f"Entry cannot be posted. Current status: {entry_status}"
        )
    
    if entry.get("xero_journal_id"):
        raise HTTPException(status_code=400, detail="Entry already posted to Xero")
    
    # Validate required account codes
    expense_account_code = schedule_data.get("expenseAccountCode")
    amortization_account_code = schedule_data.get("amortizationAccountCode")
    
    if not expense_account_code or not amortization_account_code:
        raise HTTPException(
            status_code=400,
            detail="Missing required account codes"
        )
    
    # Get Xero client with proper config (same as sync consumer)
    client_config = {
        "GCP_PROJECT_ID": os.getenv("GCP_PROJECT_ID"),
        "XERO_CLIENT_ID": os.getenv("XERO_CLIENT_ID"),
        "XERO_CLIENT_SECRET": os.getenv("XERO_CLIENT_SECRET"), 
        "XERO_REDIRECT_URI": os.getenv("XERO_REDIRECT_URI_REST_API"),
        "XERO_SCOPES": os.getenv("XERO_SCOPES", "accounting.transactions accounting.settings offline_access")
    }
    
    xero_client = await XeroApiClient.create(
        platform_org_id=entity_id,
        tenant_id=client_id,
        config=client_config
    )
    
    # Get transaction details for narration
    transaction_details = None
    supplier_name = "Unknown Supplier"
    invoice_ref = schedule_data.get('transactionReference', 'Invoice')
    invoice_date = ""
    
    if transaction_id:
        try:
            transaction_details = await transaction_service.get_transaction_by_id(transaction_id)
            if transaction_details:
                # Extract supplier name - it's in metadata, added by transformer
                if transaction_details.metadata and 'contact_name' in transaction_details.metadata:
                    supplier_name = transaction_details.metadata['contact_name']
                
                # Get invoice reference and date
                if transaction_details.document_number:
                    invoice_ref = transaction_details.document_number
                if transaction_details.date_issued:
                    invoice_date = transaction_details.date_issued.strftime('%d/%m/%Y')
        except Exception as e:
            print(f"Error getting transaction details for narration: {e}")
    
    try:
        # Prepare journal data
        journal_date = entry.get("monthDate")
        journal_amount = entry.get("amount")
        
        # Build narration using helper (custom or auto-generated)
        entry_context = {
            "transaction_details": transaction_details,
            "entry_index": entry_index,
            "total_entries": 1  # Single entry posting
        }
        narration = build_narration(schedule_data, entry_context)
        
        journal_data = {
            "narration": narration,
            "journalDate": journal_date.isoformat() if hasattr(journal_date, 'isoformat') else str(journal_date),
            "status": "DRAFT",
            "lines": [
                {
                    "accountCode": expense_account_code,
                    "description": f"Amortization expense - Entry {entry_index+1}",
                    "amount": journal_amount,
                    "isDebit": True,
                    "isCredit": False
                },
                {
                    "accountCode": amortization_account_code,
                    "description": f"Prepayment asset reduction - Entry {entry_index+1}",
                    "amount": journal_amount,
                    "isDebit": False,
                    "isCredit": True
                }
            ]
        }
        
        # Create journal in Xero
        journal_result = await xero_client.create_manual_journal(journal_data=journal_data)
        
        if journal_result and journal_result.get("ManualJournals") and not journal_result.get("error"):
            xero_journal = journal_result["ManualJournals"][0]
            xero_journal_id = xero_journal.get("ManualJournalID")
            
            # Update entry
            monthly_entries[entry_index].update({
                "status": "posted",
                "posted_at": datetime.now().isoformat(),
                "xero_journal_id": xero_journal_id,
                "lastActionByUserId": current_user.uid,
                "lastActionTimestamp": datetime.now().isoformat()
            })
            
            # Check if all entries are now posted and update schedule status accordingly
            all_posted = all(entry.get("status") == "posted" for entry in monthly_entries)
            update_data = {
                "monthlyEntries": monthly_entries,
                "updated_at": SERVER_TIMESTAMP
            }
            
            # If all entries are posted, update schedule status to "posted"
            current_schedule_status = schedule_data.get("status")
            if all_posted and current_schedule_status != "posted":
                update_data["status"] = "posted"
                update_data["posted_at"] = SERVER_TIMESTAMP
                update_data["posted_by"] = current_user.uid
                print(f"Updating schedule {schedule_id} status from {current_schedule_status} to posted - all {len(monthly_entries)} entries are posted")
            
            # Update schedule
            await schedule_ref.update(update_data)
            
            # Create audit log entry
            await _create_audit_log_entry(
                db=db,
                event_category="AMORTIZATION_MANAGEMENT",
                event_type="INDIVIDUAL_ENTRY_POSTED",
                client_id=client_id,
                entity_id=entity_id,
                transaction_id=transaction_id,
                schedule_id=schedule_id,
                status="SUCCESS",
                details={
                    "entry_index": entry_index,
                    "amount": journal_amount,
                    "xero_journal_id": xero_journal_id,
                    "user_email": current_user.email
                },
                user_id=current_user.uid
            )
            
            return {
                "success": True,
                "message": f"Entry {entry_index+1} posted successfully",
                "xero_journal_id": xero_journal_id,
                "entry_index": entry_index,
                "amount": journal_amount
            }
        else:
            error_detail = journal_result.get("error", "Unknown error") if journal_result else "No response from Xero"
            raise HTTPException(status_code=500, detail=f"Failed to post: {error_detail}")
            
    except Exception as e:
        # Update entry with error
        monthly_entries[entry_index].update({
            "status": "posting_failed",
            "error": str(e),
            "lastActionByUserId": current_user.uid,
            "lastActionTimestamp": datetime.now().isoformat()
        })
        
        await schedule_ref.update({
            "monthlyEntries": monthly_entries,
            "updated_at": SERVER_TIMESTAMP
        })
        
        raise HTTPException(status_code=500, detail=f"Error posting entry: {str(e)}")


@router.post("/{schedule_id}/entries/bulk-post")
async def post_bulk_entries(
    schedule_id: str = Path(..., description="The amortization schedule ID"),
    entry_indices: List[int] = Body(..., description="List of entry indices to post"),
    current_user: AuthUser = Depends(get_current_user),
    db=Depends(get_db),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """Posts multiple monthly entries from an amortization schedule to Xero."""
    
    if not entry_indices:
        raise HTTPException(status_code=400, detail="No entry indices provided")
    
    # Get the schedule
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("client_id")
    entity_id = schedule_data.get("entity_id")
    transaction_id = schedule_data.get("transaction_id")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    monthly_entries = schedule_data.get("monthlyEntries", [])
    max_index = len(monthly_entries) - 1
    
    # Validate indices
    invalid_indices = [idx for idx in entry_indices if idx < 0 or idx > max_index]
    if invalid_indices:
        raise HTTPException(status_code=400, detail=f"Invalid indices: {invalid_indices}")
    
    # Validate required account codes
    expense_account_code = schedule_data.get("expenseAccountCode")
    amortization_account_code = schedule_data.get("amortizationAccountCode")
    
    if not expense_account_code or not amortization_account_code:
        raise HTTPException(
            status_code=400,
            detail="Missing required account codes"
        )
    
    # Get Xero client with proper config (same as sync consumer)
    client_config = {
        "GCP_PROJECT_ID": os.getenv("GCP_PROJECT_ID"),
        "XERO_CLIENT_ID": os.getenv("XERO_CLIENT_ID"),
        "XERO_CLIENT_SECRET": os.getenv("XERO_CLIENT_SECRET"), 
        "XERO_REDIRECT_URI": os.getenv("XERO_REDIRECT_URI_REST_API"),
        "XERO_SCOPES": os.getenv("XERO_SCOPES", "accounting.transactions accounting.settings offline_access")
    }
    
    print(f"DEBUG: Creating XeroApiClient with platform_org_id={entity_id}, tenant_id={client_id}")
    xero_client = await XeroApiClient.create(
        platform_org_id=entity_id,
        tenant_id=client_id,
        config=client_config
    )
    print(f"DEBUG: XeroApiClient created. Access token exists: {bool(xero_client.access_token)}, Refresh token exists: {bool(xero_client.refresh_token)}")
    
    success_count = 0
    error_count = 0
    results = []
    
    for entry_index in entry_indices:
        entry = monthly_entries[entry_index]
        
        # Skip if already posted or invalid status
        if entry.get("xero_journal_id") or entry.get("status") not in ["proposed", "due_for_posting", "posting_failed"]:
            error_count += 1
            results.append({"entry_index": entry_index, "success": False, "error": "Cannot post entry"})
            continue
        
        # Get transaction details for narration
        transaction_details = None
        supplier_name = "Unknown Supplier"
        invoice_ref = schedule_data.get('transactionReference', 'Invoice')
        invoice_date = ""
        
        if transaction_id:
            try:
                transaction_details = await transaction_service.get_transaction_by_id(transaction_id)
                if transaction_details:
                    # Extract supplier name from metadata or contact info
                    if hasattr(transaction_details, 'metadata') and transaction_details.metadata:
                        supplier_name = transaction_details.metadata.get('contact_name', 'Unknown Supplier')
                    elif hasattr(transaction_details, 'contact_id') and transaction_details.contact_id:
                        supplier_name = f"Contact-{transaction_details.contact_id}"
                    
                    # Get invoice reference and date
                    if hasattr(transaction_details, 'document_number') and transaction_details.document_number:
                        invoice_ref = transaction_details.document_number
                    if hasattr(transaction_details, 'date_issued') and transaction_details.date_issued:
                        invoice_date = transaction_details.date_issued.strftime('%d/%m/%Y')
            except Exception as e:
                print(f"Error getting transaction details for narration: {e}")
        
        try:
            # Build narration using helper (custom or auto-generated)
            entry_context = {
                "transaction_details": transaction_details,
                "entry_index": entry_index,
                "total_entries": len(monthly_entries)
            }
            narration = build_narration(schedule_data, entry_context)
            
            journal_data = {
                "narration": narration,
                "journalDate": entry.get("monthDate").isoformat() if hasattr(entry.get("monthDate"), 'isoformat') else str(entry.get("monthDate")),
                "status": "DRAFT",
                "lines": [
                    {
                        "accountCode": schedule_data.get("expenseAccountCode"),
                        "description": f"Expense - Entry {entry_index+1}",
                        "amount": entry.get("amount"),
                        "isDebit": True,
                        "isCredit": False
                    },
                    {
                        "accountCode": schedule_data.get("amortizationAccountCode"),
                        "description": f"Asset reduction - Entry {entry_index+1}",
                        "amount": entry.get("amount"),
                        "isDebit": False,
                        "isCredit": True
                    }
                ]
            }
            
            journal_result = await xero_client.create_manual_journal(journal_data=journal_data)
            
            if journal_result and journal_result.get("ManualJournals"):
                xero_journal_id = journal_result["ManualJournals"][0].get("ManualJournalID")
                
                monthly_entries[entry_index].update({
                    "status": "posted",
                    "posted_at": datetime.now().isoformat(),
                    "xero_journal_id": xero_journal_id,
                    "lastActionByUserId": current_user.uid,
                    "lastActionTimestamp": datetime.now().isoformat()
                })
                
                success_count += 1
                results.append({"entry_index": entry_index, "success": True, "xero_journal_id": xero_journal_id})
            else:
                # Capture the actual Xero API error response
                error_detail = "Unknown Xero error"
                if journal_result:
                    if journal_result.get("error"):
                        error_detail = str(journal_result["error"])
                    elif journal_result.get("ValidationErrors"):
                        error_detail = f"Validation errors: {journal_result['ValidationErrors']}"
                    elif journal_result.get("ErrorMessage"):
                        error_detail = journal_result["ErrorMessage"]
                    else:
                        error_detail = f"Xero API response: {str(journal_result)}"
                else:
                    error_detail = "No response from Xero API"
                
                monthly_entries[entry_index].update({
                    "status": "posting_failed",
                    "error": error_detail,
                    "lastActionByUserId": current_user.uid,
                    "lastActionTimestamp": datetime.now().isoformat()
                })
                error_count += 1
                results.append({"entry_index": entry_index, "success": False, "error": error_detail})
                
        except Exception as e:
            monthly_entries[entry_index].update({
                "status": "posting_failed",
                "error": str(e),
                "lastActionByUserId": current_user.uid,
                "lastActionTimestamp": datetime.now().isoformat()
            })
            error_count += 1
            results.append({"entry_index": entry_index, "success": False, "error": str(e)})
    
    # Always check if all entries are posted and update schedule status accordingly
    # This handles both new postings and existing posted entries
    all_posted = all(entry.get("status") == "posted" for entry in monthly_entries)
    update_data = {
        "monthlyEntries": monthly_entries,
        "updated_at": SERVER_TIMESTAMP
    }
    
    # If all entries are posted, update schedule status to "posted"
    current_schedule_status = schedule_data.get("status")
    if all_posted and current_schedule_status != "posted":
        update_data["status"] = "posted"
        update_data["posted_at"] = SERVER_TIMESTAMP
        update_data["posted_by"] = current_user.uid
        print(f"Updating schedule {schedule_id} status from {current_schedule_status} to posted - all {len(monthly_entries)} entries are posted")
    
    # Update schedule
    await schedule_ref.update(update_data)
    
    # Create audit log entry
    await _create_audit_log_entry(
        db=db,
        event_category="AMORTIZATION_MANAGEMENT",
        event_type="BULK_ENTRIES_POSTED",
        client_id=client_id,
        entity_id=entity_id,
        transaction_id=transaction_id,
        schedule_id=schedule_id,
        status="SUCCESS",
        details={
            "entry_indices": entry_indices,
            "total_entries": len(entry_indices),
            "successful_posts": success_count,
            "failed_posts": error_count,
            "results": results,
            "user_email": current_user.email
        },
        user_id=current_user.uid
    )
    
    return {
        "success": True,
        "message": f"Bulk posting: {success_count} successful, {error_count} failed",
        "summary": {"total": len(entry_indices), "successful": success_count, "failed": error_count},
        "details": results
    }

@router.put("/{schedule_id}/entries/{entry_index}", response_model=MonthlyEntryUpdateResponse)
async def update_monthly_entry(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    entry_index: int = Path(..., description="Index of the monthly entry to update"),
    request: MonthlyEntryUpdateRequest = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Update the amount for a specific monthly entry in an amortization schedule."""
    
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("client_id") or schedule_data.get("clientId")
    entity_id = schedule_data.get("entity_id") or schedule_data.get("entityId")
    transaction_id = schedule_data.get("transaction_id") or schedule_data.get("transactionId")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Get monthly entries
    monthly_entries = schedule_data.get("monthlyEntries", [])
    
    if entry_index < 0 or entry_index >= len(monthly_entries):
        raise HTTPException(
            status_code=400,
            detail=f"Invalid entry index. Must be between 0 and {len(monthly_entries)-1}"
        )
    
    # Check if schedule allows edits (not already posted)
    current_status = schedule_data.get("status")
    if current_status == "posted":
        raise HTTPException(
            status_code=400,
            detail="Cannot edit monthly entries of a posted schedule"
        )
    
    # Check if the specific entry is already posted
    entry = monthly_entries[entry_index]
    entry_status = entry.get("status", "pending")
    if entry_status == "posted":
        raise HTTPException(
            status_code=400,
            detail=f"Cannot edit entry {entry_index} - it has already been posted to Xero"
        )
    
    # Store original amount for audit log
    original_amount = entry.get("amount", 0)
    
    # Update the monthly entry amount while preserving all other fields
    monthly_entries[entry_index].update({
        "amount": request.amount,
        "lastActionByUserId": current_user.uid,
        "lastActionTimestamp": datetime.now().isoformat()
    })
    
    # Update the entire monthlyEntries array to preserve all fields
    update_data = {
        "monthlyEntries": monthly_entries,
        "updated_at": SERVER_TIMESTAMP,
        "lastActionByUserId": current_user.uid,
        "lastActionTimestamp": datetime.now().isoformat()
    }
    
    # Update the schedule in Firestore
    await schedule_ref.update(update_data)
    
    # Create audit log entry
    await _create_audit_log_entry(
        db=db,
        event_category="AMORTIZATION_MANAGEMENT",
        event_type="MONTHLY_ENTRY_UPDATED",
        client_id=client_id,
        entity_id=entity_id,
        transaction_id=transaction_id,
        schedule_id=schedule_id,
        status="SUCCESS",
        details={
            "entry_index": entry_index,
            "original_amount": original_amount,
            "new_amount": request.amount,
            "month_date": entry.get("monthDate", ""),
            "user_email": current_user.email
        },
        user_id=current_user.uid
    )
    
    # Prepare response data
    updated_entry = {
        "entry_index": entry_index,
        "amount": request.amount,
        "month_date": entry.get("monthDate", ""),
        "status": entry_status
    }
    
    return MonthlyEntryUpdateResponse(
        success=True,
        message="Monthly entry updated successfully",
        updated_entry=updated_entry
    )
