import { api } from '@/lib/api';
import type {
  PrepaymentsFilters,
  PrepaymentsResponse,
  SupplierData,
  InvoiceData,
  AmortizableLineItem,
  ScheduleData,
  MonthlyEntry,
  PaginationData
} from '@/types/schedule.types';
import {
  ScheduleStatus,
  EntryStatus,
  InvoiceStatus,
  SupplierStatus,
  mapBackendScheduleStatus,
  mapBackendEntryStatus,
  isActionNeededStatus,
  isActionableInvoiceStatus
} from '@/types/schedule.types';

// Re-export types for backward compatibility
export type {
  PrepaymentsFilters,
  PrepaymentsResponse,
  SupplierData,
  InvoiceData,
  AmortizableLineItem,
  ScheduleData,
  MonthlyEntry,
  PaginationData
};

export class PrepaymentsService {
  /**
   * Fetch prepayments dashboard data from the backend
   */
  static async getPrepaymentsData(filters: PrepaymentsFilters, disableCache = false): Promise<PrepaymentsResponse> {
    try {
      console.log('DEBUG: Calling getDashboardTransactions with filters:', filters);

      // Quick validation
      if (!filters.client_id) {
        return {
          suppliers: [],
          pagination: { currentPage: 1, totalItems: 0, totalPages: 0 }
        };
      }

      // Use the dashboard transactions endpoint which includes schedules
      const apiParams = {
        client_id: filters.client_id,
        entity_id: filters.entity_id,
        transaction_type: 'ACCPAY', // Focus on bills (ACCPAY) for prepayments
        require_action: filters.show_only_pending,
        status_filters: filters.status_filters, // Pass schedule status filters
        page: filters.page || 1,
        // limit: filters.limit || 100, // Remove limit to get all data
      };
      
      console.log('🔍 PrepaymentsService API Call:', {
        filtersPage: filters.page,
        apiParamsPage: apiParams.page,
        fullApiParams: apiParams
      });
      
      const response = await api.getDashboardTransactions(apiParams, {
        cache: !disableCache // Disable cache when requested
      });

      // Only log in development
      if (import.meta.env.DEV) {
        console.log('DEBUG: Dashboard API response - transactions:', response.transactions?.length || 0);
        if (response.transactions?.length > 0) {
          console.log('🔍 Sample dashboard transaction:', response.transactions[0]);
          console.log('🔍 Transaction keys:', Object.keys(response.transactions[0]));
          // Check all possible schedule-related fields
          const tx = response.transactions[0];
          if (tx.amortization_schedules) console.log('🔍 amortization_schedules:', tx.amortization_schedules);
          if (tx.schedules) console.log('🔍 schedules:', tx.schedules);
          if (tx.line_items) console.log('🔍 line_items:', tx.line_items);
          if (tx.metadata && tx.metadata.prepayment_line_items) console.log('🔍 prepayment_line_items:', tx.metadata.prepayment_line_items);
        }
      }

      if (!response) {
        throw new Error('No response from API');
      }

      // For now, let's return empty data to see if the issue is in the transformation
      if (!response.transactions || response.transactions.length === 0) {
        console.log('DEBUG: No transactions found, returning empty data');
        return {
          suppliers: [],
          pagination: {
            currentPage: response.page || 1,
            pageSize: response.limit || 10,
            totalItems: response.total || 0,
            totalPages: response.total_pages || 0,
          },
        };
      }

      // Transform the backend response to match the frontend interface
      const suppliers = this.transformToSupplierData(response.transactions, filters.supplier_filter);

      return {
        suppliers,
        pagination: {
          currentPage: response.page || 1,
          pageSize: response.limit || 10,
          totalItems: response.total || 0,
          totalPages: response.total_pages || 0,
        },
      };
    } catch (error) {
      console.error('Error fetching prepayments data:', error);
      throw new Error('Failed to fetch prepayments data');
    }
  }

  /**
   * Transform backend dashboard items to supplier-grouped data
   */
  private static transformToSupplierData(dashboardItems: any[], supplierFilter?: string): SupplierData[] {
    const supplierMap = new Map<string, SupplierData>();

    if (!dashboardItems || !Array.isArray(dashboardItems)) {
      console.warn('Dashboard items is not an array:', dashboardItems);
      return [];
    }

    dashboardItems.forEach(item => {
      if (!item || !item.transaction) {
        console.warn('Invalid dashboard item:', item);
        return;
      }

      const transaction = item.transaction;
      const schedules = item.schedules || [];

      // Debug what the dashboard API actually returns
      console.log(`🔍 Transaction ${transaction.id} has ${schedules.length} schedules:`, {
        transactionId: transaction.id,
        reference: transaction.document_number,
        scheduleCount: schedules.length,
        scheduleIds: schedules.map(s => s.id),
        schedules: schedules.map(s => ({ id: s.id, amount: s.amount, description: s.description }))
      });

      // Extract supplier information from transaction
      const supplierId = transaction.contact?.contact_id || transaction.contact_id || 'unknown';
      const supplierName = transaction.metadata?.contact_name || transaction.contact?.name || 'Unknown Supplier';

      // Skip if supplier filter is applied and doesn't match
      if (supplierFilter && !supplierName.toLowerCase().includes(supplierFilter.toLowerCase())) {
        return;
      }

      // Get or create supplier entry
      if (!supplierMap.has(supplierId)) {
        supplierMap.set(supplierId, {
          supplierId,
          supplierName,
          overallStatus: SupplierStatus.PROPOSED,
          invoices: [],
        });
      }

      const supplier = supplierMap.get(supplierId)!;

      // Transform transaction to invoice data
      const invoiceData: InvoiceData = {
        invoiceId: transaction.id,
        reference: transaction.document_number || transaction.notes || 'N/A',
        invoiceDate: transaction.date_issued,
        totalAmount: transaction.total || 0,
        currencyCode: transaction.currency || 'USD',
        hasAttachment: Boolean(transaction.has_attachments),
        attachmentId: transaction.attachment_id,
        ocrWarningMessage: transaction.ocr_warning,
        overallStatus: this.determineInvoiceStatus(schedules),
        isPartialApplication: false,
        amortizableLineItems: this.transformSchedulesToLineItems(schedules),
      };



      supplier.invoices.push(invoiceData);

      // Update supplier overall status based on invoice statuses
      supplier.overallStatus = this.determineSupplierStatus(supplier.invoices);
    });

    return Array.from(supplierMap.values());
  }

  /**
   * Transform amortization schedules to line items
   */
  private static transformSchedulesToLineItems(schedules: any[]): AmortizableLineItem[] {
    console.log(`📋 Transforming ${schedules.length} schedules to line items`);
    return schedules.map(schedule => {
      // Debug log calculation method
      console.log('Transform schedule', schedule.id, 'calculation_method', schedule.calculation_method);
      // Use actual monthly entries from the backend if available
      let monthlyBreakdown = this.transformMonthlyEntries(schedule.monthly_entries || []);
      
      // Only generate preview if no monthly entries exist AND it's not a pending_configuration status
      // (pending_configuration schedules should have monthly entries from the backend)
      if (Object.keys(monthlyBreakdown).length === 0 && schedule.amount && schedule.status !== 'pending_configuration') {
        const startDate = schedule.entry_date || schedule.amortizationStartDate || schedule.start_date || '2025-05-01';
        monthlyBreakdown = this.generatePreviewMonthlyBreakdown(
          schedule.amount,
          startDate,
          12 // Default to 12 months
        );
      }
      
      const result = {
        lineItemId: schedule.id,
        description: schedule.description || 'Amortization Schedule',
        lineAmount: schedule.amount || 0,
        scheduleId: schedule.id,
        prepaymentAccountCode: schedule.account_code || schedule.amortizationAccountCode || '',
        expenseAccountCode: schedule.expense_account_code || schedule.expenseAccountCode || '',
        calculationMethod: schedule.calculation_method || schedule.calculationMethod || 'equal_monthly',
        overallStatus: mapBackendScheduleStatus(schedule.status),
        monthlyBreakdown,
      };
      return result;
    });
  }

  /**
   * Transform monthly entries to breakdown format
   */
  private static transformMonthlyEntries(monthlyEntries: any[]): Record<string, MonthlyEntry> {
    const breakdown: Record<string, MonthlyEntry> = {};

    monthlyEntries.forEach(entry => {
      // Handle both 'date' and 'monthDate' field names
      const dateField = entry.date || entry.monthDate;
      let monthKey = '';
      
      if (dateField) {
        try {
          // Handle different date formats
          let dateObj: Date;
          
          if (typeof dateField === 'string') {
            // Handle ISO string format
            if (dateField.includes('T') || dateField.includes('-')) {
              dateObj = new Date(dateField);
            } else {
              // Handle other string formats
              dateObj = new Date(dateField);
            }
          } else if (dateField instanceof Date) {
            dateObj = dateField;
          } else if (dateField && typeof dateField === 'object' && dateField.toDate) {
            // Handle Firebase Timestamp objects
            dateObj = dateField.toDate();
          } else {
            // Try to convert to Date
            dateObj = new Date(dateField);
          }
          
          // Extract YYYY-MM format
          if (!isNaN(dateObj.getTime())) {
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            monthKey = `${year}-${month}`;
          }
        } catch (error) {
          console.warn('Failed to parse date field:', dateField, error);
        }
      }
      
      if (monthKey) {
        breakdown[monthKey] = {
          status: entry.status || 'proposed', // Direct status, no mapping needed
          amount: entry.amount || 0,
          month_date: dateField, // Use original date field
          posted_journal_id: entry.journal_id || entry.postedJournalId,
          posting_error: entry.error_message || entry.postingError,
        };
      }
    });

    return breakdown;
  }

  /**
   * Generate preview monthly breakdown for unconfigured schedules
   */
  private static generatePreviewMonthlyBreakdown(
    totalAmount: number,
    startDate: string,
    numberOfMonths: number
  ): Record<string, MonthlyEntry> {
    const breakdown: Record<string, MonthlyEntry> = {};
    
    const monthlyAmount = totalAmount / numberOfMonths;
    const start = new Date(startDate);
    
    for (let i = 0; i < numberOfMonths; i++) {
      const currentDate = new Date(start);
      currentDate.setMonth(start.getMonth() + i);
      const monthKey = currentDate.toISOString().substring(0, 7); // YYYY-MM format
      
      breakdown[monthKey] = {
        status: 'proposed',
        amount: monthlyAmount,
        month_date: currentDate.toISOString().split('T')[0], // Full date in YYYY-MM-DD format
      };
    }
    
    return breakdown;
  }

  /**
   * Determine invoice status based on schedules
   */
  private static determineInvoiceStatus(schedules: any[]): InvoiceStatus {
    if (!schedules.length) return InvoiceStatus.VALIDATION_ERROR;

    const hasErrors = schedules.some(s => s.status === 'validation_failed');
    if (hasErrors) return InvoiceStatus.VALIDATION_ERROR;

    const hasActionNeeded = schedules.some(s => s.status === 'proposed' || s.status === 'pending_configuration' || s.status === 'requires_action');
    if (hasActionNeeded) return InvoiceStatus.ACTION_NEEDED;

    const allPosted = schedules.every(s => s.status === 'posted' || s.status === 'fully_posted');
    if (allPosted) return InvoiceStatus.FULLY_POSTED;

    const somePosted = schedules.some(s => s.status === 'posted' || s.status === 'partially_posted');
    if (somePosted) return InvoiceStatus.PARTIALLY_POSTED;

    const allSkipped = schedules.every(s => s.status === 'skipped');
    if (allSkipped) return InvoiceStatus.SKIPPED;

    const allConfirmed = schedules.every(s => s.status === 'confirmed');
    if (allConfirmed) return InvoiceStatus.CONFIRMED;

    return InvoiceStatus.PROPOSED;
  }

  /**
   * Determine supplier status based on invoices
   */
  private static determineSupplierStatus(invoices: InvoiceData[]): SupplierStatus {
    if (!invoices.length) return SupplierStatus.PROPOSED;

    const hasErrors = invoices.some(i => i.overallStatus === InvoiceStatus.VALIDATION_ERROR);
    if (hasErrors) return SupplierStatus.VALIDATION_ERROR;

    const hasActionNeeded = invoices.some(i => i.overallStatus === InvoiceStatus.ACTION_NEEDED || i.overallStatus === InvoiceStatus.PARTIALLY_POSTED);
    if (hasActionNeeded) return SupplierStatus.ACTION_NEEDED;

    const allPosted = invoices.every(i => i.overallStatus === InvoiceStatus.FULLY_POSTED);
    if (allPosted) return SupplierStatus.OK;

    const allConfirmed = invoices.every(i => i.overallStatus === InvoiceStatus.CONFIRMED);
    if (allConfirmed) return SupplierStatus.CONFIRMED;

    return SupplierStatus.PROPOSED;
  }



  /**
   * Confirm a schedule
   */
  static async confirmSchedule(scheduleId: string): Promise<void> {
    try {
      await api.confirmSchedule(scheduleId);
    } catch (error) {
      console.error('Error confirming schedule:', error);
      throw new Error('Failed to confirm schedule');
    }
  }

  /**
   * Skip a schedule
   */
  static async skipSchedule(scheduleId: string, reason: string): Promise<void> {
    try {
      await api.skipSchedule(scheduleId, reason);
    } catch (error) {
      console.error('Error skipping schedule:', error);
      throw new Error('Failed to skip schedule');
    }
  }

  /**
   * Update a schedule
   */
  static async updateSchedule(scheduleId: string, scheduleData: Partial<ScheduleData>): Promise<{
    message: string;
    schedule_id: string;
    status_progression?: {
      from: string;
      to: string;
    };
  }> {
    try {
      const response = await api.updateSchedule(scheduleId, scheduleData);
      return response;
    } catch (error) {
      console.error('Error updating schedule:', error);
      throw new Error('Failed to update schedule');
    }
  }

  /**
   * Get schedule details
   */
  static async getSchedule(scheduleId: string): Promise<any> {
    try {
      return await api.getSchedule(scheduleId);
    } catch (error) {
      console.error('Error fetching schedule:', error);
      throw new Error('Failed to fetch schedule details');
    }
  }

  /**
   * Mark schedule entries as ready for posting
   */
  static async markScheduleReady(scheduleId: string, months?: string[]): Promise<{ message: string }> {
    try {
      console.log('PrepaymentsService: Marking schedule ready:', scheduleId, months);
      
      const requestData = months && months.length > 0 
        ? { months }
        : {}; // Mark all entries ready if no specific months provided
      
      const response = await api.post<{ message: string }>(`/schedules/${scheduleId}/mark-ready`, requestData);
      
      return response;
    } catch (error) {
      console.error('Error marking schedule ready:', error);
      throw new Error('Failed to mark schedule ready');
    }
  }

  /**
   * Post schedule entries to Xero accounting system (bulk operation)
   * Status: confirmed → posted (when all entries posted)
   */
  static async postReadyEntries(scheduleId: string, entryIndices?: number[]): Promise<{ 
    message: string;
    posted_count: number;
    failed_count: number;
    results: Array<{
      entry_index: number;
      status: 'posted' | 'failed';
      journal_id?: string;
      error?: string;
    }>;
  }> {
    try {
      console.log('PrepaymentsService: Bulk posting entries to Xero:', scheduleId, entryIndices);
      
      // Send direct array as request body, not an object with entry_indices property
      const requestData = entryIndices && entryIndices.length > 0 
        ? entryIndices  // Direct array [0, 1, 2]
        : []; // Empty array if no specific indices provided
      
      console.log('PrepaymentsService: Request data for bulk-post:', requestData);
      
      const response = await api.post<{ 
        message: string;
        posted_count: number;
        failed_count: number;
        results: Array<{
          entry_index: number;
          status: 'posted' | 'failed';
          journal_id?: string;
          error?: string;
        }>;
      }>(`/schedules/${scheduleId}/entries/bulk-post`, requestData);
      
      return response;
    } catch (error) {
      console.error('Error posting entries to Xero:', error);
      if (error.response?.status === 422) {
        console.error('422 Validation Error Details:', error.response.data);
      }
      throw new Error('Failed to post entries to Xero');
    }
  }

  /**
   * Post individual schedule entry to Xero accounting system
   */
  static async postSingleEntry(scheduleId: string, entryIndex: number): Promise<{
    message: string;
    status: 'posted' | 'failed';
    journal_id?: string;
    error?: string;
  }> {
    try {
      console.log('PrepaymentsService: Posting single entry to Xero:', scheduleId, entryIndex);
      
      const response = await api.post<{
        message: string;
        status: 'posted' | 'failed';
        journal_id?: string;
        error?: string;
      }>(`/schedules/${scheduleId}/entries/${entryIndex}/post`);
      
      return response;
    } catch (error) {
      console.error('Error posting single entry to Xero:', error);
      throw new Error('Failed to post entry to Xero');
    }
  }

  /**
   * Calculate schedule preview without saving
   */
  static async calculatePreview(
    data: {
      amount: number;
      start_date: string;
      end_date: string;
      calculation_method: 'auto' | 'day_based' | 'equal_monthly';
      entity_id: string;
    },
    options?: { signal?: AbortSignal }
  ): Promise<{
    calculation_method: string;
    total_months: number;
    monthly_entries: Array<{
      month_date: string;
      amount: number;
    }>;
  }> {
    try {
      console.log('PrepaymentsService: Calculating preview:', data);
      
      const response = await api.post<{
        calculation_method: string;
        total_months: number;
        monthly_entries: Array<{
          month_date: string;
          amount: number;
        }>;
      }>('/schedules/calculate-preview', data, options);
      
      return response;
    } catch (error) {
      console.error('Error calculating preview:', error);
      throw new Error('Failed to calculate schedule preview');
    }
  }

  /**
   * Recalculate existing schedule with new parameters
   * Resets status back to proposed
   */
  static async recalculateSchedule(scheduleId: string, data: {
    amount?: number;
    start_date?: string;
    end_date?: string;
    calculation_method?: 'day_based' | 'equal_monthly';
    custom_narration?: string;
  }): Promise<any> {
    try {
      console.log('PrepaymentsService: Recalculating schedule:', scheduleId, data);
      
      const response = await api.put(`/schedules/${scheduleId}/recalculate`, data);
      
      return response;
    } catch (error) {
      console.error('Error recalculating schedule:', error);
      throw new Error('Failed to recalculate schedule');
    }
  }

  /**
   * Preview changes to schedule without saving
   */
  static async previewChanges(scheduleId: string, data: {
    amount?: number;
    start_date?: string;
    end_date?: string;
    calculation_method?: 'day_based' | 'equal_monthly';
  }): Promise<{
    monthly_entries: Array<{
      month_date: string;
      amount: number;
    }>;
    calculation_method: string;
  }> {
    try {
      console.log('PrepaymentsService: Previewing changes:', scheduleId, data);
      
      const response = await api.put<{
        monthly_entries: Array<{
          month_date: string;
          amount: number;
        }>;
        calculation_method: string;
      }>(`/schedules/${scheduleId}/preview-changes`, data);
      
      return response;
    } catch (error) {
      console.error('Error previewing changes:', error);
      throw new Error('Failed to preview changes');
    }
  }

  /**
   * Update individual monthly entry amount
   * Uses new backend endpoint: PUT /schedules/{scheduleId}/entries/{entryIndex}
   */
  static async updateMonthlyEntry(scheduleId: string, entryIndex: number, amount: number): Promise<{
    success: boolean;
    message: string;
    updated_entry: {
      entry_index: number;
      amount: number;
      month_date: string;
      status: string;
    };
  }> {
    try {
      console.log('PrepaymentsService: Updating monthly entry:', scheduleId, entryIndex, amount);
      
      const response = await api.put<{
        success: boolean;
        message: string;
        updated_entry: {
          entry_index: number;
          amount: number;
          month_date: string;
          status: string;
        };
      }>(`/schedules/${scheduleId}/entries/${entryIndex}`, {
        amount
      });
      
      return response;
    } catch (error) {
      console.error('Error updating monthly entry:', error);
      throw new Error('Failed to update monthly entry');
    }
  }

  /**
   * Get attachment blob URL for viewing
   */
  static async getAttachmentUrl(attachmentId: string): Promise<string> {
    try {
      console.log('PrepaymentsService: Fetching attachment:', attachmentId);
      
      const blobUrl = await api.getAttachmentBlob(attachmentId);
      
      return blobUrl;
    } catch (error) {
      console.error('Error fetching attachment:', error);
      throw new Error('Failed to load attachment');
    }
  }
}
