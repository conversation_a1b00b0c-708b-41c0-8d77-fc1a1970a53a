#!/usr/bin/env python3
"""
Get Request ID from send attempt for <PERSON><PERSON><PERSON> support
"""

import requests
import json

NYLAS_API_KEY = "nyk_v0_BlT3sXqnvDnmP3R8nWgn9ngMiMW0RZL6WeforK3GcznWbIPwbLO4noOzHgBb3oqy"
GRANT_ID = "8ede108b-5335-47bf-9a4a-325e60f2b2b8"
API_URI = "https://api.eu.nylas.com/v3"

def get_send_request_id():
    """Attempt send and capture Request ID for support"""
    print("📤 Attempting send to capture Request ID...")
    
    headers = {
        'Authorization': f'Bearer {NYLAS_API_KEY}',
        'Content-Type': 'application/json'
    }
    
    test_message = {
        "to": [{"email": "<EMAIL>"}],
        "from": [{"email": "<EMAIL>"}],
        "subject": "Support Test - Request ID Capture",
        "body": "This is for Nylas support troubleshooting"
    }
    
    try:
        response = requests.post(
            f"{API_URI}/grants/{GRANT_ID}/messages",
            headers=headers,
            json=test_message,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        # Look for request ID in headers or response
        request_id = None
        if 'x-request-id' in response.headers:
            request_id = response.headers['x-request-id']
        elif 'request-id' in response.headers:
            request_id = response.headers['request-id']
        
        # Try to extract from JSON response
        if not request_id:
            try:
                data = response.json()
                request_id = data.get('request_id')
            except:
                pass
        
        if request_id:
            print(f"\n🎯 REQUEST ID FOR SUPPORT: {request_id}")
        else:
            print("\n❌ Could not find Request ID in response")
            print("Check response headers above for any ID fields")
        
        return request_id
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def main():
    print("🔍 Capturing Request ID for Nylas Support")
    print("=" * 50)
    print("IMPORTANT: Update GRANT_ID with your fresh grant first!")
    print()
    
    request_id = get_send_request_id()
    
    if request_id:
        print(f"\n📋 Send this to Nylas Support:")
        print(f"   Request ID: {request_id}")
        print(f"   Grant ID: {GRANT_ID}")
        print(f"   Issue: 403 Forbidden when sending email")

if __name__ == "__main__":
    main()