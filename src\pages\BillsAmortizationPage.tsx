import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuthStore } from '../store/auth.store';
import { useFirmName } from '../hooks/useFirmName';
import { useBillsData } from '../hooks/useBillsData';
import { useSelection } from '../hooks/useSelection';
import { useKeyboardNavigation } from '../hooks/useKeyboardNavigation';
import { useAmortizationConfig } from '../hooks/useAmortizationConfig';
import { useURLSync, type ValidationLists } from '../hooks/useURLSync';
import { useAccpaySelections, useBillsAmortizationSelections } from '../store/navigation.store';
import { PostProcessingService } from '../services/post-processing.service';
import { toast } from 'sonner';
import { api } from '@/lib/api';
import { EntitiesService } from '@/services/entities.service';
import { HierarchicalBillsList } from '../components/prepayments/HierarchicalBillsList';
import { AmortizationSummary } from '../components/prepayments/AmortizationSummary';
import { AmortizationConfiguration } from '../components/prepayments/AmortizationConfiguration';
import { MonthlyScheduleTable } from '../components/prepayments/MonthlyScheduleTable';
import { SideBySideReview } from '../components/prepayments/SideBySideReview';
import { BillsToolbar } from '../components/prepayments/BillsToolbar';
import { EmptyAmortizationState } from '../components/prepayments/EmptyAmortizationState';
import { KeyboardShortcutsModal } from '../components/prepayments/KeyboardShortcutsModal';
import { SkipConfirmationModal } from '../components/prepayments/SkipConfirmationModal';
import { ResizablePanel } from '../components/ui/resizable-panel';
import { ScrollArea } from '../components/ui/scroll-area';
import { PrepaymentsService } from '../services/prepayments.service';
import type { InvoiceData, AmortizableLineItem } from '../types/schedule.types';
import { ScheduleStatus, isTerminalStatus, calculateAggregateScheduleStatus, mapBackendScheduleStatus } from '../types/schedule.types';
import type { AmortizationConfig } from '../hooks/useAmortizationConfig';
import type { MonthlyScheduleEntry } from '../types/schedule.types';
import { withRunningBalance } from '../utils/schedule-calculations';
import type { 
  SupplierNode, 
  ExpandedState, 
  HierarchicalSelection,
  HierarchicalBillsData 
} from '../types/hierarchical-bills.types';
import { 
  transformToHierarchicalData, 
  filterHierarchyBySearch
} from '../types/hierarchical-bills.types';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import { Separator } from '../components/ui/separator';
import { SidebarTrigger } from '../components/ui/sidebar';


export function BillsAmortizationPage() {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { firmName, isLoading: firmNameLoading } = useFirmName();
  // Use accpay store for client/entity (backward compatibility with old prepayments page)
  const {
    selectedClientId,
    selectedEntityId,
    setClientId,
    setEntityId,
  } = useAccpaySelections();
  
  // Use bills amortization store only for status filters
  const {
    selectedStatusFilters,
    setStatusFilters,
  } = useBillsAmortizationSelections();

  // URL synchronization with basic validation (before data loading)
  const { isHydrated, announceMessage, clearAnnouncement } = useURLSync({
    currentState: {
      clientId: selectedClientId,
      entityId: selectedEntityId,
      statusFilters: selectedStatusFilters,
    },
    handlers: {
      onClientChange: setClientId,
      onEntityChange: setEntityId,
      onStatusFiltersChange: setStatusFilters,
    },
    validationLists: {
      // Will validate against known status filters, clients/entities validated once loaded
      statusFilters: ['pending_configuration', 'proposed', 'confirmed', 'posted', 'partially_posted', 'skipped', 'cancelled', 'error', 'excluded']
    },
    enableDebug: import.meta.env.DEV,
  });

  // Extract data management to custom hook (after URL sync)
  const billsData = useBillsData(
    selectedClientId, 
    selectedEntityId, 
    selectedStatusFilters,
    setClientId,
    setEntityId,
    isHydrated
  );
  const {
    hierarchicalData,
    setHierarchicalData,
    clients,
    entities,
    isLoading,
    isLoadingClients,
    isLoadingEntities,
    error,
    pagination,
    handleDataRefresh,
    invalidateCache,
    handleLoadMore,
  } = billsData;

  // Extract selection management to custom hook
  const selection = useSelection(hierarchicalData, setHierarchicalData);
  const {
    selectedSummary,
    handleToggleSelection,
    handleSelectAll,
    getCurrentSelectionState,
    restoreSelectionState,
  } = selection;


  // UI state
  const [expandedState, setExpandedState] = useState<ExpandedState>({
    suppliers: new Set(),
    invoices: new Set(),
  });
  const [searchTerm, setSearchTerm] = useState('');

  // Attachment viewing state
  const [attachmentModalOpen, setAttachmentModalOpen] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [attachmentData, setAttachmentData] = useState<{
    transactionData: any;
    scheduleData: any;
    attachmentUrl: string;
  } | null>(null);
  const [attachmentLoading, setAttachmentLoading] = useState(false);
  const [attachmentError, setAttachmentError] = useState<string | null>(null);

  // Skip confirmation modal state
  const [skipModalOpen, setSkipModalOpen] = useState(false);
  const [skipLoading, setSkipLoading] = useState(false);

  // Xero short code for deep links
  const [xeroShortCode, setXeroShortCode] = useState<string | null>(null);

  useEffect(() => {
    if (selectedEntityId && selectedEntityId !== 'all') {
      (async () => {
        try {
          const entity: any = await EntitiesService.getEntity(selectedEntityId);
          setXeroShortCode(entity?.settings?.xero_short_code || entity?.xero_short_code || null);
        } catch (e) {
          console.warn('Could not fetch entity for short code', e);
        }
      })();
    }
  }, [selectedEntityId]);


  // Toolbar state
  const [toolbarLoading, setToolbarLoading] = useState({
    exporting: false,
    resetting: false,
    saving: false,
    posting: false,
    skipping: false,
  });

  // Optimistic UI helpers from PostProcessingService
  const optimisticHelpers = PostProcessingService.createOptimisticHelpers(setToolbarLoading);

  // Handle expand/collapse - defined early for hook usage
  const handleToggleExpand = (type: 'supplier' | 'invoice', id: string) => {
    setExpandedState(prev => {
      const newState = { ...prev };
      if (type === 'supplier') {
        const newSuppliers = new Set(prev.suppliers);
        if (newSuppliers.has(id)) {
          newSuppliers.delete(id);
        } else {
          newSuppliers.add(id);
        }
        newState.suppliers = newSuppliers;
      } else {
        const newInvoices = new Set(prev.invoices);
        if (newInvoices.has(id)) {
          newInvoices.delete(id);
        } else {
          newInvoices.add(id);
        }
        newState.invoices = newInvoices;
      }
      return newState;
    });
  };

  // Handle attachment viewing
  const handleAttachmentClick = async (invoiceId: string, attachmentId: string) => {
    try {
      setAttachmentLoading(true);
      setAttachmentError(null);
      setSelectedInvoiceId(invoiceId);

      // Find the invoice data in our hierarchical structure
      let foundInvoice: any = null;
      let foundSupplier: any = null;
      
      for (const supplier of hierarchicalData.suppliers) {
        for (const invoice of supplier.invoices) {
          if (invoice.invoiceId === invoiceId) {
            foundInvoice = invoice;
            foundSupplier = supplier;
            break;
          }
        }
        if (foundInvoice) break;
      }

      if (!foundInvoice) {
        throw new Error('Invoice not found');
      }

      // Get attachment URL (assuming api.getAttachmentBlob method exists)
      const attachmentUrl = await api.getAttachmentBlob(attachmentId);

      // Fetch raw transaction from backend for richer line-item data
      let enrichedLineItems: any[] | null = null;
      try {
        const txn = await api.getTransaction(foundInvoice.invoiceId);
        const rawItems = txn.line_items || txn.lineItems || txn.raw_xero_data?.LineItems || [];
        enrichedLineItems = rawItems.map((item: any, idx: number) => ({
          lineItemId: item.LineItemID || item.line_item_id || `idx-${idx}`,
          itemCode:    item.ItemCode       || item.item_code       || '',
          description: item.Description    || item.description    || '',
          quantity:    item.Quantity       ?? item.quantity       ?? 1,
          unitPrice:   item.UnitAmount     ?? item.unit_amount    ?? item.UnitPrice ?? 0,
          accountCode: item.AccountCode    || item.account_code   || '',
          taxType:     item.TaxType        || item.tax_type       || '',
          lineAmount:  item.LineAmount     ?? item.line_amount    ?? 0,
        }));
      } catch (e) {
        console.warn('⚠️  Could not fetch raw transaction for attachment view:', e);
      }

      // Transform invoice data for SideBySideReview component
      const transactionData = {
        transactionId: foundInvoice.invoiceId,
        reference: foundInvoice.reference,
        counterpartyName: foundSupplier.supplierName,
        hasAttachment: foundInvoice.hasAttachment,
        attachmentId: foundInvoice.attachmentId,
        invoiceDate: foundInvoice.invoiceDate,
        dueDate: foundInvoice.dueDate,
        totalAmount: foundInvoice.totalAmount,
        subtotal: foundInvoice.subtotal,
        taxTotal: foundInvoice.taxTotal,
        currencyCode: foundInvoice.currencyCode,
        // Always construct a Xero deep-link. If an organisation short-code is not yet available we
        // fall back to the generic URL that still lets Xero route the user to the invoice once they
        // pick/land on the correct organisation.
        // Build Xero deep-link. Normalise the organisation code so it has exactly one leading '!'.
        xeroUrl: xeroShortCode
          ? (() => {
              const normalisedCode = xeroShortCode.startsWith('!') ? xeroShortCode : `!${xeroShortCode}`;
              return `https://go.xero.com/organisationlogin/default.aspx?shortcode=${normalisedCode}&redirecturl=/AccountsPayable/View.aspx?InvoiceID=${foundInvoice.invoiceId}`;
            })()
          : `https://go.xero.com/AccountsPayable/View.aspx?InvoiceID=${foundInvoice.invoiceId}`,
        lineItems: enrichedLineItems ?? foundInvoice.lineItems.map((item: any) => ({
          lineItemId: item.lineItemId,
          description: item.description,
          lineAmount: item.lineAmount,
          accountCode: item.accountCode || '',
        }))
      };

      // Create schedule summary from selected line items
      const scheduleData = foundInvoice.lineItems.some((item: any) => item.isSelected) ? {
        status: 'proposed',
        originalAmount: foundInvoice.lineItems
          .filter((item: any) => item.isSelected)
          .reduce((sum: number, item: any) => sum + item.lineAmount, 0),
        amortizationStartDate: new Date().toISOString().split('T')[0], // Placeholder
        amortizationEndDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Placeholder
        numberOfPeriods: 12, // Placeholder
        amortizationAccountCode: foundInvoice.lineItems.find((item: any) => item.isSelected)?.prepaymentAccountCode || '',
        expenseAccountCode: foundInvoice.lineItems.find((item: any) => item.isSelected)?.expenseAccountCode || null
      } : null;

      setAttachmentData({
        transactionData,
        scheduleData,
        attachmentUrl
      });

      setAttachmentModalOpen(true);
    } catch (error: any) {
      console.error('Error loading attachment:', error);
      setAttachmentError(error.message || 'Failed to load attachment');
    } finally {
      setAttachmentLoading(false);
    }
  };

  // Filter hierarchical data based on search term
  const filteredSuppliers = useMemo(() => {
    return filterHierarchyBySearch(hierarchicalData.suppliers, searchTerm);
  }, [hierarchicalData.suppliers, searchTerm]);

  // Calculate summary data from selected line items using selectedSummary from hook
  const summaryData = useMemo(() => {
    const { totalAmount, lineItemIds: selectedLineItemIds } = selectedSummary;

    // Get all months from selected line items to determine date range
    const allMonthKeys: string[] = [];
    const selectedScheduleIds: string[] = [];
    const selectedLineItemStatuses: ScheduleStatus[] = [];
    let currencyCode = 'GBP'; // All schedules are in entity base currency (GBP)

    hierarchicalData.suppliers.forEach(supplier => {
      supplier.invoices.forEach(invoice => {
        invoice.lineItems.forEach(lineItem => {
          if (lineItem.isSelected) {

            // Collect schedule IDs for selected line items
            if (lineItem.scheduleId && !selectedScheduleIds.includes(lineItem.scheduleId)) {
              selectedScheduleIds.push(lineItem.scheduleId);
            }

            // Collect statuses for aggregation
            const mappedStatus = mapBackendScheduleStatus(lineItem.status);
            selectedLineItemStatuses.push(mappedStatus);

            if (lineItem.monthlyBreakdown) {
              Object.keys(lineItem.monthlyBreakdown).forEach(monthKey => {
                if (!allMonthKeys.includes(monthKey)) {
                  allMonthKeys.push(monthKey);
                }
              });
            }
          }
        });
      });
    });

    const sortedMonths = allMonthKeys.sort();
    const startDate = sortedMonths.length > 0 ? `${sortedMonths[0]}-01` : new Date().toISOString().split('T')[0];
    const endDate = sortedMonths.length > 0 ? `${sortedMonths[sortedMonths.length - 1]}-01` : startDate;
    const numberOfPeriods = sortedMonths.length;
    const monthlyAmount = numberOfPeriods > 0 ? totalAmount / numberOfPeriods : 0;

    // Calculate aggregate status from selected line items
    const aggregateStatus = calculateAggregateScheduleStatus(selectedLineItemStatuses);

    return {
      totalAmount,
      monthlyAmount,
      numberOfPeriods,
      selectedScheduleIds,
      startDate,
      endDate,
      status: aggregateStatus,
      selectedCount: selectedLineItemIds.length,
      currencyCode,
    };
  }, [selectedSummary, hierarchicalData.suppliers]);


  // Monthly schedule state - now mutable for live preview
  const [monthlySchedule, setMonthlySchedule] = useState<MonthlyScheduleEntry[]>([]);

  // Initialize monthlySchedule from hierarchical data when selection changes
  useEffect(() => {
    const allMonthKeys = new Set<string>();
    const monthlyData: Record<string, { amount: number; status: string }> = {};

    // Extract all monthly breakdowns from selected line items
    hierarchicalData.suppliers.forEach(supplier => {
      supplier.invoices.forEach(invoice => {
        invoice.lineItems.forEach(lineItem => {
          if (lineItem.isSelected && lineItem.monthlyBreakdown) {
            Object.entries(lineItem.monthlyBreakdown).forEach(([monthKey, monthData]) => {
              allMonthKeys.add(monthKey);
              if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = { amount: 0, status: monthData.status };
              }
              monthlyData[monthKey].amount += monthData.amount;
            });
          }
        });
      });
    });

    // Convert to sorted entries
    const sortedKeys = Array.from(allMonthKeys).sort((a, b) => {
      const [yearA, monthA] = a.split('-').map(Number);
      const [yearB, monthB] = b.split('-').map(Number);
      if (yearA !== yearB) return yearA - yearB;
      return monthA - monthB;
    });

    const entries = sortedKeys.map((monthKey, index) => {
      const monthData = monthlyData[monthKey];
      return {
        period: index + 1,
        date: `${monthKey}-01`, // Convert YYYY-MM to YYYY-MM-01
        amount: monthData.amount,
        originalAmount: monthData.amount, // Initialize with current amount
        status: monthData.status as 'proposed' | 'confirmed' | 'posted',
        runningBalance: 0 // Will be calculated
      };
    });

    // Calculate running balances and set the schedule
    const scheduleWithBalances = withRunningBalance(entries, summaryData.totalAmount);
    setMonthlySchedule(scheduleWithBalances);
  }, [hierarchicalData.suppliers, summaryData.totalAmount]);

  // Extract amortization configuration to custom hook (includes validation)
  const configHook = useAmortizationConfig(
    hierarchicalData,
    selectedSummary,
    summaryData,
    monthlySchedule,
    setMonthlySchedule,
    selectedEntityId && selectedEntityId !== 'all' ? selectedEntityId : undefined
  );
  const {
    amortizationConfig,
    validationMessages,
    handleConfigurationChange,
    isPreviewLoading,
  } = configHook;

  // Toolbar handlers - defined for hook usage
  const handleToolbarExport = async () => {
    const endExport = optimisticHelpers.beginExport();
    try {
      await PostProcessingService.exportScheduleData(monthlySchedule);
    } finally {
      endExport();
    }
  };

  const handleToolbarReset = async () => {
    const endReset = optimisticHelpers.beginReset();
    try {
      await PostProcessingService.resetToSavedState(
        summaryData.selectedScheduleIds,
        handleConfigurationChange,
        handleDataRefresh,
        getCurrentSelectionState,
        restoreSelectionState
      );
    } finally {
      endReset();
    }
  };

  const handleToolbarSaveChanges = async () => {
    const endSave = optimisticHelpers.beginSave();
    try {
      await PostProcessingService.saveChanges(
        summaryData.selectedScheduleIds,
        amortizationConfig,
        monthlySchedule,
        handleDataRefresh,
        getCurrentSelectionState,
        restoreSelectionState
      );
    } finally {
      endSave();
    }
  };

  const handleToolbarPostReady = async () => {
    const endPost = optimisticHelpers.beginPost();
    try {
      await PostProcessingService.saveAndPostReady(
        summaryData.selectedScheduleIds,
        amortizationConfig,
        monthlySchedule,
        handleDataRefresh,
        getCurrentSelectionState,
        restoreSelectionState
      );
    } finally {
      endPost();
    }
  };

  const handleToolbarSkip = () => {
    setSkipModalOpen(true);
  };

  const handleSkipConfirm = async (reason: string) => {
    const endSkip = optimisticHelpers.beginSkip();
    try {
      await PostProcessingService.skipSchedules(
        summaryData.selectedScheduleIds,
        reason,
        handleDataRefresh,
        getCurrentSelectionState,
        restoreSelectionState
      );
      setSkipModalOpen(false);
    } finally {
      endSkip();
    }
  };

  // Extract keyboard navigation to custom hook (fixes event listener churn)
  const keyboardNavigation = useKeyboardNavigation(
    filteredSuppliers,
    expandedState,
    selectedSummary,
    amortizationConfig,
    monthlySchedule,
    handleToggleExpand,
    handleToggleSelection,
    setHierarchicalData,
    handleLoadMore,
    pagination,
    handleToolbarPostReady,
    handleToolbarSaveChanges,
    true, // enabled
    true  // resetOnDataChange
  );
  const {
    focusedInvoiceIndex,
    navigationMode,
    focusedLineItemIndex,
    showKeyboardHelp,
    setShowKeyboardHelp,
    resetNavigation,
  } = keyboardNavigation;

  const handleScheduleEntryEdit = (period: number, updates: Partial<MonthlyScheduleEntry>) => {
    console.log('Edit schedule entry:', period, updates);
    
    // Update the monthlySchedule state directly (for live preview)
    setMonthlySchedule(prev => prev.map(entry => {
      if (entry.period === period) {
        // CRITICAL: Set originalAmount before first edit
        const originalAmount = entry.originalAmount ?? entry.amount;
        return { 
          ...entry, 
          ...updates, 
          originalAmount 
        };
      }
      return entry;
    }));
  };

  const handleScheduleExport = () => {
    console.log('Export schedule');
    // Implement export functionality
  };



  // Handle client/entity changes
  const handleClientChange = (clientId: string) => {
    setClientId(clientId);
  };

  const handleEntityChange = (entityId: string) => {
    setEntityId(entityId);
  };

  const handleStatusFiltersChange = (filters: string[]) => {
    setStatusFilters(filters);
  };

  // Validation helper functions for toolbar logic
  const hasUnpostedEntries = (schedule: any[]): boolean => {
    return schedule.some(entry => entry.status !== 'posted');
  };

  const hasMandatoryFieldsPopulated = (config: any): boolean => {
    return !!(config.prepaymentAccount && config.expenseAccount);
  };

  const amortizationCoversTotal = (schedule: any[], totalAmount: number): boolean => {
    if (schedule.length === 0 || totalAmount === 0) return false;
    
    const scheduleTotal = schedule.reduce((sum, entry) => sum + entry.amount, 0);
    // Allow small rounding differences (within 0.01)
    return Math.abs(scheduleTotal - totalAmount) <= 0.01;
  };

  // Helper function to determine if schedules can be skipped
  const canSkipSchedules = (): boolean => {
    return summaryData.selectedCount > 0 && 
           (summaryData.status === ScheduleStatus.PENDING_CONFIGURATION || 
            summaryData.status === ScheduleStatus.PROPOSED);
  };



  if (!selectedClientId) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">No client selected</p>
      </div>
    );
  }

  // Create the left panel content
  const leftPanelContent = (
    <HierarchicalBillsList
      suppliers={filteredSuppliers}
      expandedState={expandedState}
      searchTerm={searchTerm}
      onSearchChange={setSearchTerm}
      onToggleExpand={handleToggleExpand}
      onToggleSelection={handleToggleSelection}
      onSelectAll={handleSelectAll}
      onAttachmentClick={handleAttachmentClick}
      isLoading={isLoading}
      error={error}
      clients={clients}
      entities={entities}
      selectedClientId={selectedClientId}
      selectedEntityId={(selectedEntityId && selectedEntityId !== 'all') ? selectedEntityId : null}
      isLoadingClients={isLoadingClients}
      isLoadingEntities={isLoadingEntities}
      onClientChange={handleClientChange}
      onEntityChange={handleEntityChange}
      selectedStatusFilters={selectedStatusFilters}
      onStatusFiltersChange={handleStatusFiltersChange}
      focusedInvoiceIndex={focusedInvoiceIndex}
      navigationMode={navigationMode}
      focusedLineItemIndex={focusedLineItemIndex}
      pagination={{
        hasMore: pagination.hasMore,
        isLoadingMore: pagination.isLoadingMore,
        totalItems: pagination.totalItems,
        currentPage: pagination.currentPage,
      }}
      onLoadMore={handleLoadMore}
    />
  );

  // Determine if schedule is read-only (posted or terminal) - memoized to avoid recalculations
  const scheduleReadOnly = useMemo(() => 
    summaryData.selectedCount === 0 ||
    summaryData.status === ScheduleStatus.POSTED ||
    summaryData.status === ScheduleStatus.PARTIALLY_POSTED ||
    isTerminalStatus(summaryData.status as ScheduleStatus),
    [summaryData.selectedCount, summaryData.status]
  );

  // Create the right panel content - with relative positioning for toolbar
  const rightPanelContent = (
    <div className="h-full relative">
      {summaryData.selectedCount === 0 ? (
        // Show empty state when no line items are selected
        <EmptyAmortizationState 
          focusedInvoiceIndex={focusedInvoiceIndex}
          totalInvoices={filteredSuppliers.flatMap(supplier => supplier.invoices).length}
        />
      ) : (
        // Show normal content when line items are selected
        <>
          <ScrollArea className="h-full">
            <div className="p-6 space-y-6 pb-32">
              {/* Schedule Summary */}
              <div>
                <AmortizationSummary
                  totalAmount={summaryData.totalAmount}
                  monthlyAmount={summaryData.monthlyAmount}
                  numberOfPeriods={summaryData.numberOfPeriods}
                  startDate={summaryData.startDate}
                  endDate={summaryData.endDate}
                  status={summaryData.status}
                  selectedBillsCount={summaryData.selectedCount}
                  currencyCode={summaryData.currencyCode}
                  selectedCount={summaryData.selectedCount}
                />
              </div>

              {/* Configuration Form */}
              <div>
                <AmortizationConfiguration
                  config={amortizationConfig}
                  onChange={handleConfigurationChange}
                  disabled={summaryData.selectedCount === 0}
                  readOnly={scheduleReadOnly && summaryData.selectedCount > 0}
                  entityId={(selectedEntityId && selectedEntityId !== 'all') ? selectedEntityId : undefined}
                  selectedCount={summaryData.selectedCount}
                />
              </div>

              {/* Schedule Table */}
              <div>
                <MonthlyScheduleTable
                  scheduleEntries={monthlySchedule}
                  onEditEntry={scheduleReadOnly ? () => {} : handleScheduleEntryEdit}
                  currencyCode={summaryData.currencyCode}
                  isLoading={isPreviewLoading}
                  selectedCount={summaryData.selectedCount}
                />
              </div>
            </div>
          </ScrollArea>
          
          {/* Bottom sticky toolbar - only show when content is active */}
          <BillsToolbar
            totalEntries={monthlySchedule.length}
            postedEntries={monthlySchedule.filter(e => e.status === 'posted').length}
            canExport={monthlySchedule.length > 0}
            canReset={summaryData.selectedCount > 0}
            canSaveChanges={
              validationMessages.canSave.length === 0 && !isPreviewLoading
            }
            canPostReady={
              summaryData.selectedCount > 0 &&
              hasUnpostedEntries(monthlySchedule) && 
              hasMandatoryFieldsPopulated(amortizationConfig) && 
              amortizationCoversTotal(monthlySchedule, summaryData.totalAmount)
            }
            canSkip={canSkipSchedules()}
            isExporting={toolbarLoading.exporting}
            isResetting={toolbarLoading.resetting}
            isSaving={toolbarLoading.saving}
            isPosting={toolbarLoading.posting}
            isSkipping={toolbarLoading.skipping}
            validationMessages={validationMessages}
            selectedCount={summaryData.selectedCount}
            onExport={handleToolbarExport}
            onReset={handleToolbarReset}
            onSaveChanges={handleToolbarSaveChanges}
            onPostReady={handleToolbarPostReady}
            onSkip={handleToolbarSkip}
          />
        </>
      )}
    </div>
  );

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Header - Fixed height */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  navigate('/dashboard');
                }}
                className="cursor-pointer"
              >
                {firmNameLoading ? 'Loading...' : firmName}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbLink
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  navigate('/accpay/all');
                }}
                className="cursor-pointer"
              >
                ACCPAY
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Prepayments</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>

      {/* Main content - Fill remaining space with resizable panels */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanel
          leftPanel={leftPanelContent}
          rightPanel={rightPanelContent}
          defaultLeftWidth={640}
          minLeftWidth={360}
          maxLeftWidth={800}
        />
      </div>

      {/* Attachment viewing modal */}
      <SideBySideReview
        isOpen={attachmentModalOpen}
        onClose={() => {
          setAttachmentModalOpen(false);
          setAttachmentData(null);
          setSelectedInvoiceId(null);
          setAttachmentError(null);
        }}
        transactionId={selectedInvoiceId}
        transactionData={attachmentData?.transactionData || null}
        scheduleData={attachmentData?.scheduleData || null}
        attachmentUrl={attachmentData?.attachmentUrl || null}
        isLoading={attachmentLoading}
        error={attachmentError}
        entityId={selectedEntityId}
      />

      <KeyboardShortcutsModal
        open={showKeyboardHelp}
        onOpenChange={setShowKeyboardHelp}
      />

      <SkipConfirmationModal
        isOpen={skipModalOpen}
        onClose={() => setSkipModalOpen(false)}
        onConfirm={handleSkipConfirm}
        isLoading={toolbarLoading.skipping}
        selectedCount={summaryData.selectedCount}
        scheduleType="schedule"
      />
      
      {/* Accessibility announcements for filter changes */}
      {announceMessage && (
        <div 
          role="status"
          aria-live="polite" 
          aria-atomic="true" 
          className="sr-only"
          onAnimationEnd={clearAnnouncement}
        >
          {announceMessage}
        </div>
      )}
    </div>
  );
}